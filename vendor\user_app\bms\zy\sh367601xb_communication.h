#ifndef SH367601XB_COMMUNICATION_H
#define SH367601XB_COMMUNICATION_H


extern void sh367601b_comm_reset(void);
extern void sh367601b_comm_write_command(void);
extern void sh367601b_comm_write_data(unsigned char data, unsigned char addr);
extern void sh367601b_comm_read_rom(unsigned char addr, unsigned char recv_len);
extern void sh367601b_comm_read_ram(unsigned char addr, unsigned char recv_len);
#endif /* SH367601XB_COMMUNICATION_H */