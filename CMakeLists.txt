# This is generated by Telink cdt2cmake
cmake_minimum_required(VERSION 3.10)
project(BMS_Base LANGUAGES ASM C)

set(CMAKE_VERBOSE_MAKEFILE ON)
# toolchain : tc32
#set(TOOLCHAIN_PATH C:\\TelinkIDE\\opt\\tc32)
set(CMAKE_C_COMPILER ${TOOLCHAIN_PATH}/bin/tc32-elf-gcc)
set(CMAKE_ASM_COMPILER ${TOOLCHAIN_PATH}/bin/tc32-elf-gcc)
set(CMAKE_LINKER ${TOOLCHAIN_PATH}/bin/tc32-elf-ld)
set(CMAKE_OBJDUMP ${TOOLCHAIN_PATH}/bin/tc32-elf-objdump)
set(CMAKE_OBJCOPY ${TOOLCHAIN_PATH}/bin/tc32-elf-objcopy)
set(CMAKE_PRINTSIZE ${TOOLCHAIN_PATH}/bin/tc32-elf-size)
set(CMAKE_C_LINK_EXECUTABLE "<CMAKE_LINKER> <FLAGS> <LINK_FLAGS> <OBJECTS> -o <TARGET> <LINK_LIBRARIES>")
set(CMAKE_SKIP_RPATH True)
set(CMAKE_LINK_LIBRARY_SUFFIX )
set(ProjDirPath ${CMAKE_CURRENT_SOURCE_DIR}/.)

function(include_sources RESULT_VAR RELATIVE_PATH)
    file(GLOB_RECURSE SOURCES
        RELATIVE "${CMAKE_CURRENT_SOURCE_DIR}" "${CMAKE_CURRENT_SOURCE_DIR}/${RELATIVE_PATH}/*.c" "${CMAKE_CURRENT_SOURCE_DIR}/${RELATIVE_PATH}/*.S"
    )
    set(${RESULT_VAR} ${${RESULT_VAR}} ${SOURCES} PARENT_SCOPE)
endfunction()

function(include_source RESULT_VAR RELATIVE_PATH)
    set(${RESULT_VAR} ${${RESULT_VAR}} ${RELATIVE_PATH} PARENT_SCOPE)
endfunction()

include_source(825x_ble_sample_SOURCES "div_mod.S")
include_sources(825x_ble_sample_SOURCES "application")
include_sources(825x_ble_sample_SOURCES "boot/B85")
include_sources(825x_ble_sample_SOURCES "common")
include_sources(825x_ble_sample_SOURCES "drivers/8258")
include_sources(825x_ble_sample_SOURCES "vendor")

add_executable(825x_ble_sample ${825x_ble_sample_SOURCES} )

set_target_properties(825x_ble_sample PROPERTIES SUFFIX "")

# assemble_opts_define
set(ASM_DEFINE_OPTS -DMCU_STARTUP_8258)
target_compile_options(825x_ble_sample PRIVATE $<$<COMPILE_LANGUAGE:ASM>: ${ASM_DEFINE_OPTS}>)

# assemble_opts_include

# assemble_opts_contents
set(ASM_OPTS  -ffunction-sections -fdata-sections -Wall)
target_compile_options(825x_ble_sample PRIVATE $<$<COMPILE_LANGUAGE:ASM>: ${ASM_OPTS}>)

# c_opts_define
set(C_DEFINE_OPTS -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x)
target_compile_options(825x_ble_sample PRIVATE $<$<COMPILE_LANGUAGE:C>: ${C_DEFINE_OPTS}>)

# c_opts_include
set(C_INCLUDE_OPTS -I${ProjDirPath}/./ -I${ProjDirPath}/vendor/common -I${ProjDirPath}/common -I${ProjDirPath}/drivers/8258 -I${ProjDirPath}/vendor/user_app/bms -I${ProjDirPath}/vendor/user_app/bms/zhongying)
target_compile_options(825x_ble_sample PRIVATE $<$<COMPILE_LANGUAGE:C>: ${C_INCLUDE_OPTS}>)

# c_opts_contents
set(C_OPTS -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall)
target_compile_options(825x_ble_sample PRIVATE $<$<COMPILE_LANGUAGE:C>: ${C_OPTS}>)

target_link_directories(825x_ble_sample PRIVATE ${ProjDirPath}/proj_lib)
target_link_options(825x_ble_sample PRIVATE -T${ProjDirPath}/./boot.link --gc-sections)

target_link_libraries(825x_ble_sample PRIVATE lt_825x soft-fp lt_general_stack)


# prebuild postbuild

add_custom_command(TARGET 825x_ble_sample POST_BUILD COMMAND sh ${ProjDirPath}/./tl_check_fw.sh   825x_ble_sample   ${PROJECT_NAME})

# The following is the default generated postbuild, which can be annotated if not needed
add_custom_command(TARGET 825x_ble_sample POST_BUILD COMMAND ${CMAKE_OBJCOPY} -O binary  $<TARGET_FILE:825x_ble_sample> $<TARGET_FILE_DIR:825x_ble_sample>/825x_ble_sample.bin)

add_custom_command(TARGET 825x_ble_sample POST_BUILD COMMAND ${CMAKE_OBJDUMP} -x -l -S  -D $<TARGET_FILE:825x_ble_sample> > $<TARGET_FILE_DIR:825x_ble_sample>/825x_ble_sample.lst)

add_custom_command(TARGET 825x_ble_sample POST_BUILD COMMAND ${CMAKE_PRINTSIZE}  -t $<TARGET_FILE:825x_ble_sample>)
