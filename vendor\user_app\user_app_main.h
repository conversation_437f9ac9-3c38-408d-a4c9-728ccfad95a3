#ifndef _USER_APP_MAIN_H_
#define _USER_APP_MAIN_H_

#include "drivers.h"
#include "tl_common.h"
#include "stack/ble/ble.h"
#include "uart/app_usart.h"
#include "../b85m_ble_sample/app.h"
#include "../b85m_ble_sample/app_att.h"
#include "../common/tlkapi_debug.h"
#include "list/list_type.h"
#include "flash/user_app_flash.h"



/* 16位大端转小端 */
#define BE16_TO_LE16(x) ((uint16_t)( \
    (((uint16_t)(x) & 0xFF00) >> 8) | \
    (((uint16_t)(x) & 0x00FF) << 8)))
/* 32位大端转小端 */ 
#define BE32_TO_LE32(x) ((uint32_t)( \
    (((uint32_t)(x) & 0xFF000000) >> 24) | \
    (((uint32_t)(x) & 0x00FF0000) >> 8)  | \
    (((uint32_t)(x) & 0x0000FF00) << 8)  | \
    (((uint32_t)(x) & 0x000000FF) << 24)))



/* 电池状态 */
typedef enum 
{
    BATT_IDLE_STATE,            /* 空闲 */
    BATT_DISCHARGE_STATE,       /* 放电 */
    BATT_CHARGING_STATE,        /* 充电 */
}BATT_STATE;
/* 任务序号 */
typedef enum
{
    QUEUE_CURR_TASK,
    QUEUE_RAM_TASK,
    QUEUE_ROM_TASK,
    QUEUE_WRITE_ENABLE_TASK,
    QUEUE_WRITE_01_TASK,
    QUEUE_WRITE_02_TASK,
    QUEUE_WRITE_03_TASK,
    QUEUE_WRITE_04_TASK,
    QUEUE_WRITE_05_TASK,
    QUEUE_WRITE_06_TASK,
    QUEUE_WRITE_07_TASK,
    QUEUE_WRITE_08_TASK,
    QUEUE_WRITE_09_TASK,
    QUEUE_WRITE_0A_TASK,
    QUEUE_WRITE_0B_TASK,
    QUEUE_WRITE_0C_TASK,
    QUEUE_WRITE_0D_TASK,
    QUEUE_WRITE_0E_TASK,
    QUEUE_WRITE_0F_TASK,
    QUEUE_WRITE_10_TASK,
    QUEUE_WRITE_11_TASK,
    QUEUE_WRITE_12_TASK,
    QUEUE_WRITE_13_TASK,
    QUEUE_WRITE_14_TASK,
    QUEUE_WRITE_15_TASK,
    QUEUE_RESET_TASK,
}TIME_QUEUE_TASK_FLG;
/* 每个任务执行完后的延迟时间 */
typedef enum
{
    QUEUE_CURR_TIME = 20,
    QUEUE_RAM_TIME = 80,
    QUEUE_ROM_TIME = 40,
    QUEUE_WRITE_ENABLE_TIME = 20,
    QUEUE_WRITE_TIME = 20,
    QUEUE_RESET_TIME = 100,
}TIME_QUEUE_TASK_TIME;



/* 串口结构体 */
typedef struct
{
    unsigned char len;                          /* 长度 */
    unsigned char flg;                          /* 标志 */
    unsigned char buff[255];                    /* 数据 */
}User_App_Uart_TypeDef;
/* 蓝牙结构体 */
typedef struct
{
    unsigned char connect;                      /* 连接状态 */     
    unsigned char len;                          /* 长度 */
    unsigned char flg;                          /* 标志 */
    unsigned char buff[255];                    /* 数据 */
}User_App_Ble_TypeDef;




extern void User_App_Init(void);
extern void time0_callback(void);
extern void time1_callback(void);
extern void User_App_Logic(void);

extern User_App_Uart_TypeDef Uart;
extern User_App_Ble_TypeDef  Ble;

#endif