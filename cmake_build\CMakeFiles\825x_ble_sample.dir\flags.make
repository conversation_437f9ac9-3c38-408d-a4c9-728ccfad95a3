# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# compile ASM with C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32/bin/tc32-elf-gcc
# compile C with C:\Users\<USER>\.Telink_Tools\tc32_130_Windows\tc32/bin/tc32-elf-gcc
ASM_DEFINES = 

ASM_INCLUDES = 

ASM_FLAGS = -g -DMCU_STARTUP_8258 -ffunction-sections -fdata-sections -Wall

C_DEFINES = 

C_INCLUDES = 

C_FLAGS = -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -ID:/Telink_Project/BMS_Base/././ -ID:/Telink_Project/BMS_Base/./vendor/common -ID:/Telink_Project/BMS_Base/./common -ID:/Telink_Project/BMS_Base/./drivers/8258 -ID:/Telink_Project/BMS_Base/./vendor/user_app/bms -ID:/Telink_Project/BMS_Base/./vendor/user_app/bms/zhongying -fpack-struct -fshort-enums -O2 -std=gnu99 -fshort-wchar -fms-extensions -finline-small-functions -ffunction-sections -fdata-sections -Wall

