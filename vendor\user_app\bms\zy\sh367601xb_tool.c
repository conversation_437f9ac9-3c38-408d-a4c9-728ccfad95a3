#include "sh367601xb_tool.h"



/* ==========================================ntc========================================== */
/**
 *  @brief 阻值 <-> 温度 对照表
 */
const NTC_TypeDef ntc3435[150] = 
{
    {50, 366.4286}, {51, 344.5753}, {52, 324.1796}, {53, 305.1344}, {54, 287.3413},
    {55, 270.7097}, {56, 255.1561}, {57, 240.6035}, {58, 226.9810}, {59, 214.2231},
    {60, 202.2693}, {61, 191.0637}, {62, 180.5546}, {63, 170.6944}, {64, 161.4387},
    {65, 152.7468}, {66, 144.5807}, {67, 136.9052}, {68, 129.6879}, {69, 122.8985},
    {70, 116.5089}, {71, 110.4932}, {72, 104.8272}, {73, 99.4883},  {74, 94.4558},
    {75, 89.7101}, {76, 85.2332},  {77, 81.0082},  {78, 77.0194},  {79, 73.2523},
    {80, 69.6931}, {81, 66.3291},  {82, 63.1485},  {83, 60.1402},  {84, 57.2939},
    {85, 54.5998}, {86, 52.049},   {87, 49.633},   {88, 47.3439},  {89, 45.1743},
    {90, 43.1172}, {91, 41.1663},  {92, 39.3153},  {93, 37.5587},  {94, 35.891},
    {95, 34.3074}, {96, 32.8029},  {97, 31.3734},  {98, 30.0145},  {99, 28.7225},
    {100, 27.4936}, {101, 26.3245}, {102, 25.2119}, {103, 24.1527}, {104, 23.1442},
    {105, 22.1835}, {106, 21.2682}, {107, 20.3959}, {108, 19.5644}, {109, 18.7714},
    {110, 18.0151}, {111, 17.2935}, {112, 16.6048}, {113, 15.9475}, {114, 15.3198},
    {115, 14.7203}, {116, 14.1475}, {117, 13.6003}, {118, 13.0772}, {119, 12.5771},
    {120, 12.0988}, {121, 11.6413}, {122, 11.2037}, {123, 10.7848}, {124, 10.3839},
    {125, 10.00},   {127, 9.2802},  {128, 8.9428},  {129, 8.6195},  {130, 8.3096},
    {131, 8.0124},  {132, 7.7275},  {133, 7.4541},  {134, 7.1919},  {135, 6.9403},
    {136, 6.6987},  {137, 6.4669},  {138, 6.2442},  {139, 6.0304},  {140, 5.825},
    {141, 5.6276},  {142, 5.438},   {143, 5.2557},  {144, 5.0804},  {145, 4.9119},
    {146, 4.7498},  {147, 4.5939},  {148, 4.4439},  {149, 4.2995},  {150, 4.1605},
    {151, 4.0268},  {152, 3.898},   {153, 3.7739},  {154, 3.6544},  {155, 3.5393},
    {156, 3.4284},  {157, 3.3215},  {158, 3.2185},  {159, 3.1191},  {160, 3.0234},
    {161, 2.931},   {162, 2.8419},  {163, 2.7559},  {164, 2.6729},  {165, 2.5929},
    {166, 2.5156},  {167, 2.441},   {168, 2.369},   {169, 2.2994},  {170, 2.2322},
    {171, 2.1673},  {172, 2.1046},  {173, 2.044},   {174, 1.9854},  {175, 1.9288},
    {176, 1.874},   {177, 1.8211},  {178, 1.7699},  {179, 1.7204},  {180, 1.6725},
    {181, 1.6262},  {182, 1.5813},  {183, 1.5379},  {184, 1.4959},  {185, 1.4553},
    {186, 1.4159},  {187, 1.3778},  {188, 1.3408},  {189, 1.3051},  {190, 1.2704},
    {191, 1.2368},  {192, 1.2043},  {193, 1.1728},  {194, 1.1422},  {195, 1.1126},
    {196, 1.0839},  {197, 1.056},   {198, 1.029},   {199, 1.0028},  {200, 0.9774},
};


/**
 * @brief  通过查表法计算NTC温度
 * @param  resistance_ohm 当前NTC阻值，单位：欧姆（Ω），范围：0.1-1000.0
 * @param  temp_offset_celsius 温度偏移量，单位：摄氏度（℃），通常为100用于数据存储优化
 * @return 温度值，单位：摄氏度（℃），失败返回-128
 */
char ntc_calculate_temp_from_resistance(float resistance_ohm, char temp_offset_celsius)
{
    /* 边界检查 */
    if (resistance_ohm >= ntc3435[0].resist) return ntc3435[0].temp - temp_offset_celsius;
    if (resistance_ohm <= ntc3435[NTC_TABLE_SIZE-1].resist) return ntc3435[NTC_TABLE_SIZE-1].temp - temp_offset_celsius;

    /* 二分查找 */
    unsigned short left = 0;
    unsigned short right = NTC_TABLE_SIZE - 1;
    while (left <= right)
    {
        unsigned short mid = left + (right - left) / 2;
        if (ntc3435[mid].resist == resistance_ohm)
        {
            return ntc3435[mid].temp - temp_offset_celsius;
        }
        else if (ntc3435[mid].resist < resistance_ohm)
        {
            right = mid - 1;
        }
        else
        {
            left = mid + 1;
        }
    }

    /* 返回最近的温度点（右边界） */
    return ntc3435[right].temp - temp_offset_celsius;
}
/**
 * @brief  从温度查表获取电阻值（新版本，支持自定义参数）
 * @param  temp_celsius 当前温度，单位：摄氏度（℃），范围：-50到200
 * @return 阻值，单位：千欧（kΩ），失败返回-1.0
 */
float ntc_find_resistance_from_temp(char temp_celsius, char temp_offset_celsius)
{
    char adjusted_temp = temp_celsius + temp_offset_celsius;

    /* 边界检查 */
    if (adjusted_temp <= ntc3435[0].temp) return ntc3435[0].resist;
    if (adjusted_temp >= ntc3435[NTC_TABLE_SIZE - 1].temp) return ntc3435[NTC_TABLE_SIZE - 1].resist;

    /* 二分查找 */
    int left = 0;
    int right = NTC_TABLE_SIZE - 1;

    while (left <= right)
    {
        int mid = left + (right - left) / 2;
        if (ntc3435[mid].temp == adjusted_temp)
        {
            return ntc3435[mid].resist;
        }
        else if (ntc3435[mid].temp < adjusted_temp)
        {
            left = mid + 1;
        }
        else
        {
            right = mid - 1;
        }
    }

    /* 返回最近的温度点对应的电阻值 */
    return ntc3435[right].resist;
}
/**
 * @brief  zy寄存器值转换为高温温度(ROM)
 * @param  adc_reg_value ADC温度寄存器值，范围：0x00-0x1FF（0-511）
 * @return 温度值，单位：摄氏度（℃），失败返回-128
 */
char ntc_calculate_temp_from_adc(unsigned short adc_reg_value)
{
    /* 参数验证 */
    if (adc_reg_value >= 512) {
        return -128; /* 错误返回值 */
    }

    /* 计算NTC阻值：RT = (10kΩ * reg_value) / (512 - reg_value) */
    /* 使用固定参数：pullup_resistance=10kΩ, adc_resolution=512 */
    float rt_kohm = (10.0 * adc_reg_value) / ((float)512.0f - adc_reg_value);
    return ntc_calculate_temp_from_resistance(rt_kohm, 100);
}
/**
 * @brief  zy寄存器值转换为低温温度(ROM)
 * @param  reg_value 寄存器值，范围：0x00-0xFF（0-255）
 * @return 温度值，单位：摄氏度（℃），失败返回-128
 */
char ntc_calculate_low_temp_from_reg(unsigned char reg_value)
{
    /* 使用固定参数：pullup_resistance=10kΩ, adc_resolution=512, voltage_offset=0.5V */
    double voltage_ratio = (double)reg_value / 512.0 + 0.5;
    if (voltage_ratio >= 1.0) {
        return -128; /* 避免除零错误 */
    }

    double rt_kohm = (10.0 * voltage_ratio) / (1.0 - voltage_ratio);
    return ntc_calculate_temp_from_resistance(rt_kohm, 100);
}
/**
 * @brief  外部温度寄存器值转换温度(RAM)
 * @param  temp_reg 温度寄存器值，范围：0x00-0x7FFF（0-32767）
 * @return 温度值，单位：摄氏度（℃），失败返回-128
 */
char ntc_calculate_external_temp(unsigned short temp_reg)
{
    /* 参数验证 */
    if (temp_reg >= 32768) {
        return -128; /* 错误返回值 */
    }

    /* R_T = (TEMP / (32768 - TEMP)) × 10kΩ */
    /* 使用固定参数：max_reg_value=32768, pullup_resistance=10kΩ */
    double rt_kohm = ((double)temp_reg / (32768 - temp_reg)) * 10.0;
    return ntc_calculate_temp_from_resistance(rt_kohm, 100);
}
/**
 * @brief  高温温度转换为zy寄存器值(ROM)
 * @param  temp_celsius 当前温度，单位：摄氏度（℃），范围：0-100
 * @return 寄存器值，范围：0-255，失败返回255
 */
unsigned char ntc_calculate_high_temp_reg(char temp_celsius)
{
    /* 获取对应温度的电阻值 */
    double rt_kohm = ntc_find_resistance_from_temp(temp_celsius, 100);
    if (rt_kohm < 0.0) {
        return 255; /* 错误返回值 */
    }

    /* 高温保护寄存器值计算：使用固定参数 */
    /* 上拉电阻：10kΩ，ADC分辨率：512 */
    double ratio = rt_kohm / (10.0 + rt_kohm);
    double reg_float = ratio * 512.0;

    /* 边界检查和四舍五入 */
    if (reg_float > 255.0) return 255;
    if (reg_float < 0.0) return 0;
    return (unsigned char)(reg_float);
}
/**
 * @brief  低温温度转换为zy寄存器值(ROM)
 * @param  temp_celsius 当前温度，单位：摄氏度（℃），范围：-45到25
 * @return 寄存器值，范围：0-255，失败返回255
 */
unsigned char ntc_calculate_low_temp_reg(char temp_celsius)
{
    /* 获取对应温度的电阻值 */
    double rt_kohm = ntc_find_resistance_from_temp(temp_celsius, 100);
    if (rt_kohm < 0.0) {
        return 255; /* 错误返回值 */
    }

    /* 低温保护寄存器值计算：使用固定参数 */
    /* 上拉电阻：10kΩ，ADC分辨率：512，电压偏移：0.5V */
    double ratio = rt_kohm / (10.0 + rt_kohm);
    double reg_float = (ratio - 0.5) * 512.0;

    /* 边界检查和四舍五入 */
    if (reg_float > 255.0) return 255;
    if (reg_float < 0.0) return 0;
    return (unsigned char)(reg_float);
}








/* ==========================================电流========================================== */
/**
 * @brief  ADC寄存器值转换为电流（
 * @param  adc_reg_value 电流寄存器值，范围：0-65535
 * @param  sampling_resistance_mohm 采样电阻值，单位：毫欧（mΩ），通常为0.5-10mΩ
 * @param  adc_reference_factor ADC参考因子，通常为26214.4
 * @return 电流值，单位：毫安（mA），失败返回-32768
 */
int current_calculate_from_adc(unsigned short adc_reg_value, float sampling_resistance_mohm)
{
    /* 参数验证 */
    if (sampling_resistance_mohm <= 0.0f)
        return -32768; /* 错误返回值 */

    float current_ma = (90.0 * adc_reg_value) / (26214.4 * sampling_resistance_mohm);

    return (int)current_ma;
}




/* ==========================================电压========================================== */
/**
 * @brief  寄存器值转换成电压（简化版本）
 * @param  cell 电压寄存器值
 * @return 电压，单位：毫伏（mV）
 */
unsigned short Reg_From_Voltage(unsigned short cell)
{
    /* 直接计算：(cell * 5.0) / 32 * 1000 = cell * 0.15625 */
    return (unsigned short)((cell * 5) / 32);
}





