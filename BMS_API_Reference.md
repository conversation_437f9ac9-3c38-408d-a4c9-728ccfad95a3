# BMS系统API参考文档

## 1. 核心API接口

### 1.1 BMS数据管理器API

#### bms_init()
```c
int bms_init(BMS_DataManager* self);
```
**功能**: 初始化BMS数据管理器实例  
**参数**: 
- `self`: BMS数据管理器实例指针
**返回值**: 0=成功，-1=失败

#### 管理器通用方法模式
```c
// 初始化方法
void (*init)(ManagerType* self);

// 数据处理方法（各管理器特有）
void (*process_xxx_data)(ManagerType* self, ...);
```

### 1.2 电压管理器API

#### VoltageManager_process_voltage_data()
```c
void VoltageManager_process_voltage_data(VoltageManager* self,
                                       unsigned short* cell_voltages,
                                       unsigned char cell_count,
                                       unsigned short total_voltage);
```
**功能**: 处理电压数据  
**参数**:
- `self`: 电压管理器指针
- `cell_voltages`: 电芯电压数组
- `cell_count`: 电芯数量
- `total_voltage`: 总电压

### 1.3 电流管理器API

#### CurrentManager_process_current_data()
```c
void CurrentManager_process_current_data(CurrentManager* self,
                                       int total_current,
                                       int max_charge_current,
                                       int max_discharge_current,
                                       CurrentState current_state);
```
**功能**: 处理电流数据  
**参数**:
- `self`: 电流管理器指针
- `total_current`: 总电流(mA)
- `max_charge_current`: 最大充电电流(mA)
- `max_discharge_current`: 最大放电电流(mA)
- `current_state`: 电流状态

### 1.4 温度管理器API

#### TemperatureManager_process_temperature_data()
```c
void TemperatureManager_process_temperature_data(TemperatureManager* self,
                                               signed char* external_temps,
                                               unsigned char temp_count,
                                               signed char chip_temp);
```
**功能**: 处理温度数据  
**参数**:
- `self`: 温度管理器指针
- `external_temps`: 外部温度数组
- `temp_count`: 温度传感器数量
- `chip_temp`: 芯片温度

## 2. *********设备API

### 2.1 设备初始化

#### sh367601xb_init()
```c
int sh367601xb_init(*********_Device* device);
```
**功能**: 初始化*********设备  
**参数**: `device` - 设备实例指针  
**返回值**: 0=成功，-1=失败

### 2.2 通信接口API

#### 复位操作
```c
void (*reset)(void);
```
**功能**: 复位*********芯片

#### 数据读写
```c
void (*write_data)(unsigned char data, unsigned char addr);
void (*read_rom)(unsigned char addr, unsigned char recv_len);
void (*read_ram)(unsigned char addr, unsigned char recv_len);
```
**功能**: 寄存器数据读写操作

### 2.3 配置管理API

#### 设置芯片ID
```c
void (*set_id)(*********_Device* self, unsigned char id);
```

#### 设置保护参数
```c
void (*set_eovr)(*********_Device* self, unsigned char eovr);  // 过压保护
void (*set_euvr)(*********_Device* self, unsigned char euvr);  // 欠压保护
void (*set_ocra)(*********_Device* self, unsigned char ocra);  // 过流保护
```

### 2.4 数据转换API

#### 电压转换
```c
unsigned short (*calc_voltage_from_adc)(unsigned short adc_value);
```
**功能**: ADC值转换为电压值(mV)

#### 电流转换
```c
int (*calc_current_from_adc)(unsigned short adc_value, float sampling_resistance);
```
**功能**: ADC值转换为电流值(mA)

#### 温度转换
```c
signed char (*calc_external_temp)(unsigned short temp_adc);
signed char (*calc_internal_temp)(unsigned char temp_reg);
```
**功能**: 温度ADC值转换为温度值(℃)

## 3. 工具函数API

### 3.1 SOC计算

#### soc_compute()
```c
float soc_compute(float soc, float capacity_mAh, float current_mA, 
                 float time_ms, unsigned char direction);
```
**功能**: 基于库仑计法计算SOC  
**参数**:
- `soc`: 当前SOC(%)
- `capacity_mAh`: 电池容量(mAh)
- `current_mA`: 当前电流(mA)
- `time_ms`: 时间间隔(ms)
- `direction`: 充放电方向
**返回值**: 更新后的SOC(%)

### 3.2 数字滤波

#### hybridFilter()
```c
float hybridFilter(float new_sample);
```
**功能**: 混合SMA+EMA数字滤波器  
**参数**: `new_sample` - 新采样值  
**返回值**: 滤波后的值

### 3.3 NTC温度转换

#### ntc_calculate_temp_from_adc()
```c
char ntc_calculate_temp_from_adc(unsigned short adc_reg_value);
```
**功能**: ADC值转换为NTC温度  
**参数**: `adc_reg_value` - ADC寄存器值  
**返回值**: 温度值(℃)

## 4. 定制化API

### 4.1 FN定制数据管理

#### fn_custom_data_init()
```c
void fn_custom_data_init(void);
```
**功能**: 初始化FN项目定制数据管理器

#### 定制方法接口
```c
typedef struct {
    void (*fn_Rs2058_Init)(void);
    void (*fn_Rs2058_Gpio1_Low)(void);
    void (*fn_Rs2058_Gpio1_Hight)(void);
    void (*fn_special_process)(*********_Device *device, Queue* q);
    void (*fn_current_process)(*********_Device *device);
} method;
```

## 5. 数据结构访问

### 5.1 获取管理器数据
```c
// 获取电压数据
VoltageData* voltage_data = &bms_manager.voltage_mgr.data;
unsigned short max_voltage = voltage_data->max_cell_voltage;

// 获取电流数据  
CurrentData* current_data = &bms_manager.current_mgr.data;
int total_current = current_data->total_current;

// 获取温度数据
TemperatureData* temp_data = &bms_manager.temp_mgr.data;
signed char max_temp = temp_data->max_external_temp;
```

### 5.2 获取寄存器数据
```c
// 获取ROM配置数据
User_App_Sh3607601x_Rom_TypeDef* rom = &device->rom[chip_index];
unsigned char ovp_threshold = rom->eovr;

// 获取RAM实时数据
User_App_Sh3607601x_Ram_TypeDef* ram = &device->ram[chip_index];
unsigned short cell1_voltage = ram->cell1;
```

## 6. 错误处理

### 6.1 返回值约定
- `0`: 成功
- `-1`: 一般错误
- `NULL`: 指针错误

### 6.2 参数检查
所有API函数都会进行参数有效性检查：
```c
if (self == NULL) return -1;  // 指针检查
if (data == NULL) return;     // 数据指针检查
```

## 7. 使用示例

### 7.1 基本初始化流程
```c
// 1. 创建设备实例
*********_Device device;

// 2. 初始化设备
if (sh367601xb_init(&device) != 0) {
    // 初始化失败处理
    return -1;
}

// 3. 初始化定制数据
fn_custom_data_init();
```

### 7.2 数据更新流程
```c
// 1. 读取实时数据
device.bms_sync.update_realtime_data(&device);

// 2. 获取处理后的数据
VoltageData* voltage = &device.bms_system.voltage_mgr.data;
CurrentData* current = &device.bms_system.current_mgr.data;

// 3. 应用层处理
float new_soc = soc_compute(old_soc, capacity, current->total_current, 
                           time_interval, current->current_state);
```

## 8. 使用模型版本
Claude Sonnet 4
