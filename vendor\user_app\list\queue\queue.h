#ifndef _USER_APP_QUEUE_H_
#define _USER_APP_QUEUE_H_

/*========================================================= 队列 =========================================================*/
// #include "../list.h"

#include <stdint.h>

#define QUEUE_SIZE 30

// 队列节点
typedef struct {
    uint8_t data;
    uint8_t time;
} QueueNode;

// 队列结构
typedef struct {
    QueueNode nodes[QUEUE_SIZE];  // 静态数组存储
    uint8_t front;                // 队头索引
    uint8_t rear;                 // 队尾索引
    uint8_t count;                // 当前元素数
} Queue;

void queue_init(Queue *q);
int queue_push(Queue *q, uint8_t data, uint8_t time);
int queue_push_front(Queue *q, uint8_t data, uint8_t time);
int queue_pop(Queue *q, QueueNode *node);
int queue_empty(Queue *q);
#endif // _USER_APP_QUEUE_H_