#include "sh367601xb_chip.h"
#include "../../user_app_main.h"  /* 引入队列定义 */

/**
 * @file sh367601xb_chip.c
 * @brief SH367601B芯片模块实现
 * @version 1.0
 * @date 2025
 */

/**
 * @brief 填充ROM寄存器数据到缓冲区
 * @param buff 目标缓冲区
 * @param rom ROM数据结构指针
 */
// static void Write_Rom_To_Eeprom(unsigned char *buff, User_App_Sh3607601x_Rom_TypeDef *rom)
// {
//     /* 填充字节数组 */
//     /* 00H */
//     buff[0] = rom->id;

//     /* 01H: enmosr, chys, tc, cn */
//     buff[1] = (rom->enmosr << 7) | (rom->chys << 6) | (rom->tc << 4) | rom->cn;

//     /* 02H: bals, chs, ocra, eovr, euvr, eow, eot3, enmos */
//     buff[2] = (rom->bals << 7) | (rom->chs << 6) | (rom->ocra << 5) | 
//               (rom->eovr << 4) | (rom->euvr << 3) | (rom->eow << 2) | 
//               (rom->eot3 << 1) | rom->enmos;

//     /* 03H,04H,05H: ovt, ov, ovr */
//     buff[3] = (rom->ovt << 6) | ((rom->ov >> 4) & 0x3F);
//     buff[4] = ((rom->ov & 0x0F) << 4) | ((rom->ovr >> 8) & 0x01);
//     buff[5] = rom->ovr & 0xFF;

//     /* 06H: uvr */
//     buff[6] = rom->uvr;

//     /* 07H,08H: lov, balt, uvt, uv */
//     buff[7] = (rom->lov << 5) | (rom->balt << 4) | (rom->uvt << 1) | ((rom->uv >> 8) & 0x01);
//     buff[8] = rom->uv & 0xFF;

//     /* 09H: balv */
//     buff[9] = rom->balv;

//     /* 0AH: bald, ocd1v, ocd1t */
//     buff[10] = (rom->bald << 6) | (rom->ocd1v << 2) | rom->ocd1t;

//     /* 0BH: sct, ocd2v, ocd2t */
//     buff[11] = (rom->sct << 5) | (rom->ocd2v << 2) | rom->ocd2t;

//     /* 0CH: occv, occt */
//     buff[12] = (rom->occv << 2) | rom->occt;

//     /* 0DH~14H: otc, otcr, otd, otdr, utc, utcr, utd, utdr */
//     buff[13] = rom->otc;
//     buff[14] = rom->otcr;
//     buff[15] = rom->otd;
//     buff[16] = rom->otdr;
//     buff[17] = rom->utc;
//     buff[18] = rom->utcr;
//     buff[19] = rom->utd;
//     buff[20] = rom->utdr;
// }

/**
 * @brief 切换到指定芯片
 * @param device 设备实例指针
 * @param chip_index 目标芯片索引
 */
void sh36760_switch_to_chip(SH367601B_Device* device, unsigned char chip_index)
{
    if (device == NULL || chip_index >= device->chip.data.chip_count) {
        return;
    }
    
    device->chip.data.current_chip_index = chip_index;
    
    /* 调用对应芯片的硬件切换函数 */
    if (device->chip.data.chip_switch_functions[chip_index] != NULL) 
        device->chip.data.chip_switch_functions[chip_index]();
}

/**
 * @brief 写入ROM数据（基于队列机制）
 * @param device 设备实例指针
 * @param q 队列指针
 */
void sh36760_write_rom_for_chip(SH367601B_Device* device, Queue* q)
{
    if (device == NULL || q == NULL) {
        return;
    }

    // unsigned char chip_index = device->chip.data.current_chip_index;

    // /* 使用当前芯片索引对应的缓冲区和ROM数据 */
    // Write_Rom_To_Eeprom(device->write_buff[chip_index], &device->write_rom);

    /* 添加写队列命令 */
    queue_push(q, QUEUE_WRITE_ENABLE_TASK, QUEUE_WRITE_ENABLE_TIME);
    for (unsigned char i = 0; i < ROM_ADDR_LEN; i++)
    {
        if (device->write_flags[i])
        {
            queue_push(q, QUEUE_WRITE_01_TASK + i, QUEUE_WRITE_TIME);
        }
    }
    queue_push(q, QUEUE_RESET_TASK, QUEUE_RESET_TIME);
}

/**
 * @brief 设置芯片数量
 * @param device 设备实例指针
 * @param chip_count 芯片数量
 */
void sh36760_set_chip_count(SH367601B_Device* device, unsigned char chip_count)
{
    if (device == NULL || chip_count == 0 || chip_count > MAX_CHIP_COUNT) {
        return;
    }
    
    device->chip.data.chip_count = chip_count;
    
    /* 如果当前选中的芯片索引超出新的芯片数量范围，则重置为0 */
    if (device->chip.data.current_chip_index >= chip_count) {
        device->chip.data.current_chip_index = 0;
    }
    
    printf("SH367601B: Chip count set to %d\n", chip_count);
}

/**
 * @brief 启动批量写ROM流程（自动遍历所有芯片）
 * @param device 设备实例指针
 * @param q 队列指针
 */
void sh36760_start_batch_write_rom(SH367601B_Device* device, Queue* q)
{
    if (device == NULL || q == NULL) {
        return;
    }
    
    /* 标记批量写ROM流程开始 */
    device->chip.data.write_rom_in_progress = 1;
    
    /* 从第一个芯片开始 */
    device->chip.data.current_chip_index = 0;
    
    /* 切换到第一个芯片并添加写入流程 */
    device->chip.method.switch_to_chip(device, 0);
    device->chip.method.write_rom_for_chip(device, q);
    
    printf("SH367601B: Starting batch ROM write for %d chips\n", device->chip.data.chip_count);
}

/**
 * @brief 设置单个芯片类型
 * @param device 设备实例指针
 * @param chip_index 芯片索引
 * @param chip_type 芯片类型
 */
void sh36760_set_chip_type(SH367601B_Device* device, unsigned char chip_index, SH367601B_ChipType chip_type)
{
    if (device == NULL || chip_index >= MAX_CHIP_COUNT) {
        return;
    }
    
    device->chip.data.chip_type[chip_index] = chip_type;
    
    printf("SH367601B: Chip %d type set to %d\n", chip_index, chip_type);
}

/**
 * @brief 获取单个芯片类型
 * @param device 设备实例指针
 * @param chip_index 芯片索引
 * @return 芯片类型
 */
SH367601B_ChipType sh36760_get_chip_type(SH367601B_Device* device, unsigned char chip_index)
{
    if (device == NULL || chip_index >= MAX_CHIP_COUNT) {
        return SH367601B_CHIP_10_SERIES;  /* 默认返回10串芯片 */
    }
    
    return device->chip.data.chip_type[chip_index];
}

/**
 * @brief 获取单个芯片最大串数
 * @param device 设备实例指针
 * @param chip_index 芯片索引
 * @return 最大串数
 */
unsigned char sh36760_get_chip_max_cells(SH367601B_Device* device, unsigned char chip_index)
{
    if (device == NULL || chip_index >= MAX_CHIP_COUNT) {
        return MAX_CELLS_10_SERIES;  /* 默认返回10串 */
    }
    
    SH367601B_ChipType chip_type = device->chip.data.chip_type[chip_index];
    
    switch (chip_type) {
        case SH367601B_CHIP_10_SERIES:
            return MAX_CELLS_10_SERIES;
        case SH367601B_CHIP_14_SERIES:
            return MAX_CELLS_14_SERIES;
        case SH367601B_CHIP_16_SERIES:
            return MAX_CELLS_16_SERIES;
        default:
            return MAX_CELLS_10_SERIES;
    }
}

/**
 * @brief 设置单个芯片的硬件切换函数
 * @param device 设备实例指针
 * @param chip_index 芯片索引
 * @param switch_func 芯片切换函数指针
 */
void sh36760_set_chip_switch_function(SH367601B_Device* device, unsigned char chip_index, void (*switch_func)(void))
{
    if (device == NULL || chip_index >= MAX_CHIP_COUNT) {
        return;
    }
    
    device->chip.data.chip_switch_functions[chip_index] = switch_func;
    printf("SH367601B: Chip %d switch function registered\n", chip_index);
}

/**
 * @brief 处理复位任务中的芯片切换逻辑
 * @param device 设备实例指针
 * @param q 队列指针
 * @return 0=继续处理下一个芯片，1=所有芯片处理完成
 * @note 在复位任务中调用，用于自动切换到下一个芯片并继续写ROM流程
 */
int sh36760_handle_reset_task_chip_switching(SH367601B_Device* device, Queue* q)
{
    if (device == NULL || q == NULL) {
        return 1; /* 参数错误，结束流程 */
    }
    
    /* 检查是否处于批量写ROM流程中 */
    if (!device->chip.data.write_rom_in_progress) {
        return 1; /* 不在写ROM流程中，无需处理 */
    }
    
    /* 当前芯片写入完成，检查是否还有下一颗芯片 */
    unsigned char next_chip_index = device->chip.data.current_chip_index + 1;
    
    printf("SH367601B: Chip %d ROM write completed\n", device->chip.data.current_chip_index);
    
    /* 检查是否还有更多芯片需要写入 */
    if (next_chip_index < device->chip.data.chip_count) {
        printf("SH367601B: Switching to next chip %d\n", next_chip_index);
        
        /* 切换到下一个芯片并继续写入流程 */
        sh36760_switch_to_chip(device, next_chip_index);
        sh36760_write_rom_for_chip(device, q);
        
        return 0; /* 继续处理下一个芯片 */
    } else {
        /* 所有芯片写入完成，结束批量写ROM流程 */
        queue_push(q, QUEUE_ROM_TASK, QUEUE_ROM_TIME);
        device->chip.data.write_rom_in_progress = 0;
        device->chip.data.current_chip_index = 0;  /* 重置为第一个芯片 */
        printf("SH367601B: Batch ROM write completed for all %d chips\n", device->chip.data.chip_count);
        
        return 1; /* 所有芯片处理完成 */
    }
}

/**
 * @brief 处理RAM读取任务中的芯片切换逻辑
 * @param device 设备实例指针
 * @param q 队列指针
 * @param is_rom_task 是否是ROM任务
 * @return 0=继续读取下一个芯片，1=所有芯片读取完成
 * @note 在RAM读取完成后调用，用于自动切换到下一个芯片并继续读取RAM
 */
int sh36760_handle_ram_read_chip_switching(SH367601B_Device* device, Queue* q, char is_rom_task)
{
    if (device == NULL || q == NULL || device->chip.data.write_rom_in_progress) {
        return 1; /* 参数错误，结束流程 */
    }
    
    /* 当前芯片任务完成，检查是否还有下一颗芯片 */
    unsigned char next_chip_index = device->chip.data.current_chip_index + 1;
    
    if (0 == is_rom_task) 
        printf("SH367601B: Chip %d ROM write completed\n", device->chip.data.current_chip_index);
    else if (1 == is_rom_task)
        printf("SH367601B: Chip %d RAM read completed\n", device->chip.data.current_chip_index);
    else
        printf("SH367601B: Chip %d RAM reset completed\n", device->chip.data.current_chip_index);

    /* 检查是否还有更多芯片需要处理 */
    if (next_chip_index < device->chip.data.chip_count) 
    {
        printf("SH367601B: Switching to next chip %d\n", next_chip_index);
        
        /* 切换到下一个芯片并继续任务 */
        sh36760_switch_to_chip(device, next_chip_index);
        
        if (0 == is_rom_task) 
        {
            /* ROM任务：添加ROM读取流程 */
            queue_push(q, QUEUE_ROM_TASK, QUEUE_ROM_TIME);
        } 
        else if (1 == is_rom_task)
        {
            /* RAM任务：添加RAM读取任务 */
            queue_push(q, QUEUE_RAM_TASK, QUEUE_RAM_TIME);
        }
        else 
        {
            /* 重置任务：添加重置任务 */
            queue_push_front(q, QUEUE_RESET_TASK, QUEUE_RESET_TIME);
        }
        
        return 0; /* 继续处理下一个芯片 */
    } else {
        /* 所有芯片处理完成 */
        sh36760_switch_to_chip(device, 0);
        
        if (is_rom_task) {
            printf("SH367601B: ROM read completed for all %d chips\n", device->chip.data.chip_count);
        } else {
            printf("SH367601B: RAM read completed for all %d chips\n", device->chip.data.chip_count);
        }
        
        return 1; /* 所有芯片处理完成 */
    }
}


