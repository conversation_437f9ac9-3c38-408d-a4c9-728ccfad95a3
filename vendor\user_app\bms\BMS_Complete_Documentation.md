# BMS电池管理系统完整文档

## 1. 系统概述

本BMS（Battery Management System）是基于*********芯片的嵌入式电池管理系统，采用面向对象的C语言设计，具有高度模块化、可扩展的特点。系统支持多串电池管理（10串/14串/16串），提供完整的电池监控、保护、均衡和状态管理功能。

### 1.1 系统特性
- **面向对象设计**: 采用C语言面向对象编程思想，每个模块都有独立的数据和方法
- **分层架构**: 清晰的分层设计，从硬件抽象到应用层
- **多芯片支持**: 支持最多4个*********芯片级联，扩展电池串数
- **实时监控**: 实时监控电压、电流、温度等关键参数
- **智能保护**: 多重保护机制，确保电池安全
- **定制化支持**: 支持项目特定的定制化功能

## 2. 系统架构

### 2.1 整体架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                        应用层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │  SOC计算    │  │  SOH计算    │  │  保护逻辑   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                      定制化层                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              FN_CustomDataManager                      │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │ │
│  │  │  出厂信息   │  │  电压校准   │  │  零点校准   │    │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    BMS数据管理层                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                BMS_DataManager                         │ │
│  │ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐       │ │
│  │ │报警管理 │ │状态管理 │ │电压管理 │ │电流管理 │       │ │
│  │ └─────────┘ └─────────┘ └─────────┘ └─────────┘       │ │
│  │ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐       │ │
│  │ │温度管理 │ │充放电   │ │电池状态 │ │保护参数 │       │ │
│  │ └─────────┘ └─────────┘ └─────────┘ └─────────┘       │ │
│  │ ┌─────────┐                                           │ │
│  │ │自定义   │                                           │ │
│  │ └─────────┘                                           │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    硬件抽象层                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                *********_Device                        │ │
│  │ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐       │ │
│  │ │配置管理 │ │通信驱动 │ │数据解析 │ │数据转换 │       │ │
│  │ └─────────┘ └─────────┘ └─────────┘ └─────────┘       │ │
│  │ ┌─────────┐ ┌─────────┐                               │ │
│  │ │工具函数 │ │芯片管理 │                               │ │
│  │ └─────────┘ └─────────┘                               │ │
│  │ ┌─────────────────┐ ┌─────────────────┐               │ │
│  │ │  ROM寄存器数据  │ │  RAM寄存器数据  │               │ │
│  │ │   (配置参数)    │ │   (实时数据)    │               │ │
│  │ └─────────────────┘ └─────────────────┘               │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                        工具层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐          │
│  │  SOC计算    │  │  数字滤波   │  │  NTC温度    │          │
│  │    工具     │  │    工具     │  │  转换工具   │          │
│  └─────────────┘  └─────────────┘  └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                        硬件层                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  *********芯片                          │ │
│  │  ┌─────────────┐              ┌─────────────┐          │ │
│  │  │ ROM寄存器   │              │ RAM寄存器   │          │ │
│  │  │ 0x00-0x15   │              │ 0x40-0x6E   │          │ │
│  │  │ (配置参数)  │              │ (实时数据)  │          │ │
│  │  └─────────────┘              └─────────────┘          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 3. 核心组件详解

### 3.1 BMS数据管理器 (BMS_DataManager)

BMS数据管理器是系统的核心，统一管理所有子管理器：

```c
typedef struct BMS_DataManager {
    AlarmManager alarm_mgr;                    // 报警管理器
    StatusManager status_mgr;                  // 状态管理器
    VoltageManager voltage_mgr;                // 电压管理器
    CurrentManager current_mgr;                // 电流管理器
    ChargeDischargeManager charge_discharge_mgr; // 充放电管理器
    TemperatureManager temp_mgr;               // 温度管理器
    BatteryStateManager battery_state_mgr;     // 电池状态管理器
    ProtectionParameterManager protection_param_mgr; // 保护参数管理器
    CustomParameterManager custom_param_mgr;   // 自定义参数管理器
    bool is_initialized;                       // 初始化状态
} BMS_DataManager;
```

#### 3.1.1 报警管理器 (AlarmManager)
负责管理各种报警状态：
- 充电高温/低温保护
- 放电高温/低温保护
- 充电/放电过流保护
- 过压/欠压保护

#### 3.1.2 状态管理器 (StatusManager)
管理系统运行状态：
- 均衡状态
- 充电/放电MOS状态
- 充电/放电状态

#### 3.1.3 电压管理器 (VoltageManager)
管理电压相关数据：
- 总电压、单体电压
- 最大/最小/平均电压
- 电池压差、电池串数

#### 3.1.4 电流管理器 (CurrentManager)
管理电流相关数据：
- 总电流、最大充放电电流
- 电流状态（空闲/充电/放电）

#### 3.1.5 温度管理器 (TemperatureManager)
管理温度相关数据：
- 外部温度、芯片温度、MOS温度
- 最大/最小温度

#### 3.1.6 充放电管理器 (ChargeDischargeManager)
管理充放电相关数据：
- 循环次数、剩余容量、剩余时间
- 累计充放电量、当前功率

#### 3.1.7 电池状态管理器 (BatteryStateManager)
管理电池状态信息：
- SOC（电量百分比）
- SOH（健康状态）
- 采样时间

#### 3.1.8 保护参数管理器 (ProtectionParameterManager)
管理保护参数设置：
- 过压/欠压保护阈值和延时
- 过流保护阈值和延时
- 温度保护阈值

#### 3.1.9 自定义参数管理器 (CustomParameterManager)
管理自定义参数：
- 电池总容量、剩余容量
- 采样电阻、自耗电

### 3.2 *********设备类 (*********_Device)

*********设备类是硬件抽象层的核心，继承BMS数据管理系统：

```c
struct *********_Device {
    BMS_DataManager bms_system;                // 内嵌BMS数据管理系统
    
    // 寄存器数据
    User_App_Sh3607601x_Rom_TypeDef rom[MAX_CHIP_COUNT];  // ROM寄存器数组
    User_App_Sh3607601x_Ram_TypeDef ram[MAX_CHIP_COUNT];  // RAM寄存器数组
    
    // 设备状态
    bool is_initialized;                       // 设备初始化状态
    bool is_connected;                         // 设备连接状态
    
    // 功能模块
    *********_Config config;                  // 配置管理模块
    *********_Communication comm;             // 通信驱动模块
    *********_Parser parser;                  // 数据解析模块
    *********_Converter converter;            // 数据转换模块
    *********_Tool tool;                      // 工具函数模块
    *********_Chip chip;                      // 芯片管理模块
};
```

## 4. 文件结构详解

### 4.1 核心文件组织

```
vendor/user_app/bms/
├── bms_data.h                    # BMS数据管理系统头文件
├── bms_data.c                    # BMS数据管理系统实现
├── bms_data_tool.h               # BMS工具函数头文件
├── bms_data_tool.c               # BMS工具函数实现
├── customization/                # 定制化模块目录
│   ├── fn_custom_data.h          # FN项目定制数据头文件
│   └── fn_custom_data.c          # FN项目定制数据实现
└── zy/                          # *********芯片驱动目录
    ├── sh367601xb.h             # 主设备类头文件
    ├── sh367601xb.c             # 主设备类实现
    ├── sh367601xb_chip.h        # 芯片管理模块头文件
    ├── sh367601xb_chip.c        # 芯片管理模块实现
    ├── sh367601xb_communication.h # 通信驱动头文件
    ├── sh367601xb_communication.c # 通信驱动实现
    ├── sh367601xb_config.h      # 配置管理头文件
    ├── sh367601xb_config.c      # 配置管理实现
    ├── sh367601xb_converter.h   # 数据转换头文件
    ├── sh367601xb_converter.c   # 数据转换实现
    ├── sh367601xb_parser.h      # 数据解析头文件
    ├── sh367601xb_parser.c      # 数据解析实现
    ├── sh367601xb_tool.h        # 工具函数头文件
    └── sh367601xb_tool.c        # 工具函数实现
```

### 4.2 关键宏定义

```c
// 芯片配置
#define MAX_CHIP_COUNT 4                    // 最大芯片数量
#define MAX_CELLS_10_SERIES 10             // 10串芯片最大串数
#define MAX_CELLS_14_SERIES 14             // 14串芯片最大串数
#define MAX_CELLS_16_SERIES 16             // 16串芯片最大串数

// 寄存器地址配置
#define ROM_ADDR_START 0x00                // ROM寄存器起始地址
#define ROM_ADDR_LEN   0x15                // ROM寄存器长度(21字节)
#define RAM_ADDR_START 0x40                // RAM寄存器起始地址
#define RAM_ADDR_LEN   0x2E                // RAM寄存器长度(46字节)

// FN定制配置
#define FN_FACTORY_INFO_SIZE        30     // 出厂信息大小
#define FN_PRODUCTION_DATE_SIZE     8      // 生产日期大小
#define FN_VOLTAGE_CALIB_COUNT      16     // 电压校准通道数量
```

## 5. 数据流处理机制

### 5.1 实时数据更新流程

```
硬件层(*********芯片)
    ↓ [寄存器读取]
通信驱动层(Communication)
    ↓ [原始数据]
数据解析层(Parser)
    ↓ [结构化数据]
数据转换层(Converter)
    ↓ [物理量数据]
数据管理层(各Manager)
    ↓ [处理后数据]
应用层(SOC/SOH/保护逻辑)
```

### 5.2 配置参数更新流程

```
应用层配置请求
    ↓
配置管理模块(Config)
    ↓ [参数验证]
数据转换模块(Converter)
    ↓ [寄存器值转换]
通信驱动模块(Communication)
    ↓ [写入寄存器]
硬件层(*********芯片)
```

### 5.3 定制化处理流程

```
标准BMS数据处理
    ↓
FN定制数据管理器
    ↓ [特殊处理逻辑]
    ├── 零点电流校准
    ├── 电压校准处理
    ├── 出厂信息管理
    └── 特殊业务逻辑
    ↓
最终应用数据
```

## 6. 初始化序列

### 6.1 系统初始化顺序

```c
// 1. BMS数据管理器初始化
int bms_init(BMS_DataManager* self) {
    // 初始化所有子管理器
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, alarm_mgr, AlarmManager, "Alarm", process_alarm_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, status_mgr, StatusManager, "Status", process_status_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, voltage_mgr, VoltageManager, "Voltage", process_voltage_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, current_mgr, CurrentManager, "Current", process_current_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, temp_mgr, TemperatureManager, "Temperature", process_temperature_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, charge_discharge_mgr, ChargeDischargeManager, "Charge/Discharge", process_charge_discharge_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, battery_state_mgr, BatteryStateManager, "Battery state", process_battery_state_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, protection_param_mgr, ProtectionParameterManager, "Protection param", process_protection_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, custom_param_mgr, CustomParameterManager, "Custom param", process_custom_param_data);

    self->is_initialized = true;
    return 0;
}

// 2. *********设备初始化
int sh367601b_init(*********_Device* device) {
    // 初始化内嵌BMS系统
    bms_init(&device->bms_system);

    // 初始化各功能模块
    sh367601xb_config_init(&device->config);
    sh367601xb_communication_init(&device->comm);
    sh367601xb_parser_init(&device->parser);
    sh367601xb_converter_init(&device->converter);
    sh367601xb_tool_init(&device->tool);
    sh367601xb_chip_init(&device->chip);

    // 设置BMS数据同步方法
    device->bms_sync.update_realtime_data = sh367601b_update_realtime_data;
    device->bms_sync.update_protection_config = sh367601b_update_protection_config;

    device->is_initialized = true;
    return 0;
}

// 3. 定制化数据初始化
void fn_custom_data_init(void) {
    // 初始化FN项目特定的定制数据
    // 设置定制方法指针
    // 加载校准数据
}
```

## 7. 关键算法实现

### 7.1 SOC计算算法

```c
float soc_compute(float soc, float capacity_mAh, float current_mA,
                 float time_ms, unsigned char direction) {
    // 库仑计法SOC计算
    float delta_soc = (current_mA * time_ms) / (capacity_mAh * 3600.0f * 1000.0f) * 100.0f;

    if (CURRENT_STATE_DISCHARGING == direction) {
        soc -= delta_soc;
        if (soc < 0.0f) soc = 0.0f;
    } else if (CURRENT_STATE_CHARGING == direction) {
        soc += delta_soc;
        if (soc > 100.0f) soc = 100.0f;
    }

    return soc;
}
```

### 7.2 数字滤波算法

```c
float hybridFilter(float new_sample) {
    // 混合SMA+EMA滤波器
    static float sma_buffer[N] = {0};
    static int sma_index = 0;
    static float ema = 0;

    // SMA阶段
    float sum = 0;
    sma_buffer[sma_index] = new_sample;
    sma_index = (sma_index + 1) % N;
    for (int i = 0; i < N; i++) sum += sma_buffer[i];
    float sma = sum / N;

    // EMA阶段
    float alpha = 0.35;
    ema = alpha * sma + (1 - alpha) * ema;
    return ema;
}
```

### 7.3 NTC温度转换算法

```c
char ntc_calculate_temp_from_adc(unsigned short adc_reg_value) {
    // 通过查表法将ADC值转换为温度
    // 支持温度偏移校准
    // 返回摄氏度温度值
}
```

## 8. 扩展指南

### 8.1 添加新的管理器

1. **定义管理器结构体**（在bms_data.h中）：
```c
typedef struct NewManager {
    struct {
        // 数据成员
    } data;

    struct {
        void (*init)(struct NewManager* self);
        void (*process_new_data)(struct NewManager* self, ...);
    } methods;
} NewManager;
```

2. **实现管理器方法**（在bms_data.c中）：
```c
static void NewManager_process_new_data(NewManager* self, ...) {
    // 实现数据处理逻辑
}

DEFINE_MANAGER_WITH_PROCESSOR(NewManager, process_new_data)
```

3. **添加到BMS_DataManager**：
```c
typedef struct BMS_DataManager {
    // 现有管理器...
    NewManager new_mgr;  // 新增管理器
    bool is_initialized;
} BMS_DataManager;
```

4. **在初始化函数中添加**：
```c
INIT_AND_ENABLE_PROCESSOR_MANAGER(self, new_mgr, NewManager, "New", process_new_data);
```

### 8.2 支持新的芯片型号

1. **添加芯片类型枚举**：
```c
typedef enum {
    *********_CHIP_10_SERIES = 0,
    *********_CHIP_14_SERIES = 1,
    *********_CHIP_16_SERIES = 2,
    *********_CHIP_NEW_SERIES = 3  // 新增芯片类型
} *********_ChipType;
```

2. **更新寄存器数据结构**：
```c
// 根据新芯片的寄存器定义更新ROM/RAM结构体
```

3. **实现对应的通信和解析方法**：
```c
// 在相应的模块中添加新芯片的支持
```

## 9. 使用模型版本
Claude Sonnet 4
