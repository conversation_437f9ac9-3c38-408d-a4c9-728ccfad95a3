################################################################################
# Automatically-generated file. Do not edit!
################################################################################

-include ../makefile.init

RM := rm -rf

# All of the sources participating in the build are defined here
-include sources.mk
-include vendor/user_app/uart/subdir.mk
-include vendor/user_app/system_main/subdir.mk
-include vendor/user_app/list/queue/subdir.mk
-include vendor/user_app/flash/subdir.mk
-include vendor/user_app/bms/zy/subdir.mk
-include vendor/user_app/bms/subdir.mk
-include vendor/user_app/att_ble/subdir.mk
-include vendor/user_app/at_cmd/subdir.mk
-include vendor/user_app/agreement/rzn/subdir.mk
-include vendor/user_app/agreement/subdir.mk
-include vendor/user_app/subdir.mk
-include vendor/common/subdir.mk
-include vendor/b85m_ble_sample/subdir.mk
-include drivers/8258/flash/subdir.mk
-include drivers/8258/driver_ext/subdir.mk
-include drivers/8258/subdir.mk
-include common/subdir.mk
-include cmake_build/CMakeFiles/4.1.0-rc1/CompilerIdC/subdir.mk
-include boot/B85/subdir.mk
-include application/usbstd/subdir.mk
-include application/print/subdir.mk
-include application/keyboard/subdir.mk
-include application/audio/subdir.mk
-include application/app/subdir.mk
-include subdir.mk
-include objects.mk

-include ../makefile.defs

OPTIONAL_TOOL_DEPS := \
$(wildcard ../makefile.defs) \
$(wildcard ../makefile.init) \
$(wildcard ../makefile.targets) \


BUILD_ARTIFACT_NAME := FN_Project
BUILD_ARTIFACT_EXTENSION := elf
BUILD_ARTIFACT_PREFIX :=
BUILD_ARTIFACT := $(BUILD_ARTIFACT_PREFIX)$(BUILD_ARTIFACT_NAME)$(if $(BUILD_ARTIFACT_EXTENSION),.$(BUILD_ARTIFACT_EXTENSION),)

# Add inputs and outputs from these tool invocations to the build variables 
LST += \
825x_ble_sample.lst \

FLASH_IMAGE += \

SIZEDUMMY += \
sizedummy \


# All Target
all:
	+@$(MAKE) --no-print-directory main-build && $(MAKE) --no-print-directory post-build

# Main-build Target
main-build: FN_Project.elf secondary-outputs

# Tool invocations
FN_Project.elf: $(OBJS) $(USER_OBJS) makefile objects.mk $(OPTIONAL_TOOL_DEPS)
	@echo 'Building target: $@'
	@echo 'Invoking: TC32 C Linker'
	tc32-elf-ld --gc-sections -L"D:\Telink_Project\FN_Project\proj_lib" -T ../boot.link -o"FN_Project.elf" $(OBJS) $(USER_OBJS) $(LIBS)
	@echo 'Finished building target: $@'
	@echo ' '

825x_ble_sample.lst: FN_Project.elf makefile objects.mk $(OPTIONAL_TOOL_DEPS)
	@echo 'Invoking: TC32 Create Extended Listing'
	tc32-elf-objdump -x -l -S FN_Project.elf  >"825x_ble_sample.lst"
	@echo 'Finished building: $@'
	@echo ' '

: FN_Project.elf makefile objects.mk $(OPTIONAL_TOOL_DEPS)
	@echo 'Create Flash image (binary format)'
	tc32-elf-objcopy FN_Project.elf
	@echo 'Finished building: $@'
	@echo ' '

sizedummy: FN_Project.elf makefile objects.mk $(OPTIONAL_TOOL_DEPS)
	@echo 'Invoking: Print Size'
	tc32-elf-size -t FN_Project.elf
	@echo 'Finished building: $@'
	@echo ' '

# Other Targets
clean:
	-$(RM) $(FLASH_IMAGE)$(ELFS)$(OBJS)$(LST)$(SIZEDUMMY) FN_Project.elf
	-@echo ' '

post-build:
	-"D:\Telink_Project\FN_Project/tl_check_fw.sh"   825x_ble_sample   FN_Project
	-@echo ' '

secondary-outputs: $(LST) $(FLASH_IMAGE) $(SIZEDUMMY)

.PHONY: all clean dependents main-build post-build

-include ../makefile.targets
