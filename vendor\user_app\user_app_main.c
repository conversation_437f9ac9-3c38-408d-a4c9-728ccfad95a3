#include "user_app_main.h"
#include "bms/zy/sh367601xb.h"
#include "bms/customization/fn_custom_data.h"
#include "agreement/agreement.h"
#include "flash/user_app_flash.h"



/* 函数声明 */
static void User_App_Load_Flash_Data(void);

/* 队列 */
Queue q;
static __attribute__((used)) QueueNode node;
static __attribute__((used)) int read_time;
static __attribute__((used)) char sh367601x_task_lock;
/* 全局数据 */
User_App_Ble_TypeDef  Ble;
User_App_Uart_TypeDef Uart;
*********_Device sh367601xb;
FN_CustomDataManager fn_custom_data;




/* 用户app初始化 */
void User_App_Init(void)
{
    /* 初始化*********设备 */
    sh367601b_init(&sh367601xb);
    /* rzn */
#if RZN_PROJECT_ENABLE
    // sh367601xb.chip.method.set_chip_count(&sh367601xb, 1);
    // sh367601xb.chip.method.set_chip_type(&sh367601xb, 0, *********_CHIP_16_SERIES);
#elif FN_PROJECT_ENABLE
    fn_custom_data_init();
    fn_custom_data.method.fn_Rs2058_Init();
    /* fn */
    /* 设置芯片数量 */
    sh367601xb.chip.method.set_chip_count(&sh367601xb, 2);
    /* 设置芯片切换函数 */
    sh367601xb.chip.method.set_chip_switch_function(&sh367601xb, 0, fn_custom_data.method.fn_Rs2058_Gpio1_Low);
    sh367601xb.chip.method.set_chip_switch_function(&sh367601xb, 1, fn_custom_data.method.fn_Rs2058_Gpio1_Hight);   
    /* 设置单个芯片类型 */
    sh367601xb.chip.method.set_chip_type(&sh367601xb, 0, *********_CHIP_10_SERIES);
    sh367601xb.chip.method.set_chip_type(&sh367601xb, 1, *********_CHIP_16_SERIES);
#endif
    /* 读取Flash中的所有数据 */
    User_App_Load_Flash_Data();
    /* 添加开机任务 */
    queue_push(&q, QUEUE_RESET_TASK, QUEUE_RESET_TIME);
    queue_push(&q, QUEUE_ROM_TASK, QUEUE_ROM_TIME);
    
    /* 定时器初始化 */
    timer0_set_mode(TIMER_MODE_SYSCLK,0, 20 * CLOCK_SYS_CLOCK_1MS);
    timer1_set_mode(TIMER_MODE_SYSCLK,0, 2000 * CLOCK_SYS_CLOCK_1MS);
	timer_start(TIMER0);
	timer_start(TIMER1);
    
    printf("Multi-Chip BMS System Init Completed\n");
}
/* 定时器回调函数 */
void time0_callback(void)
{
    if (sh367601x_task_lock) return;
    queue_push(&q, QUEUE_CURR_TASK, QUEUE_CURR_TIME);
}
void time1_callback(void)
{
    queue_push(&q, QUEUE_RAM_TASK, QUEUE_RAM_TIME);
}



/* 蓝牙数据处理 */
void User_App_Sh367601x_Ble_process(unsigned char *data, unsigned char len)
{
    /* 通过协议处理蓝牙数据 */
    protocol_process_data(data, len);
}
/* 串口数据处理 */
void User_App_Sh367601x_Uart_process(unsigned char *data, unsigned char len)
{
    if (0x5A == data[2])
    {
        switch (data[1])
        {
            /* 写使能 */
            case 0x0A:
            {
                printf("write cmd successful\n");
                break;
            }
            /* 写命令 */
            case 0x01:
            {
                printf("write successful\n");
                break;
            }
            /* 读rom */
            case 0x02:
            {
                /* 解析rom */
                sh367601xb.parser.method.parse_rom(&sh367601xb.rom[sh367601xb.chip.data.current_chip_index], &data[5]);
                sh367601xb.bms_sync.update_protection_config(&sh367601xb);
                sh367601xb.chip.method.handle_ram_read_chip_switching(&sh367601xb, &q, 0);
                break;
            }
            /* 读ram */
            case 0x03:
            {
                /* 处理电流 */
                if (0x02 == data[4])
                {
                    /* 直接更新芯片RAM数据 */
                    sh367601xb.ram[0].cur = (data[5] << 8) | data[6];
                    u16 cur = sh367601xb.ram[0].cur;
                    if ((sh367601xb.ram[0].cur >> 15) & 1)
                        cur = (~sh367601xb.ram[0].cur) + 1;
                    sh367601xb.bms_system.current_mgr.data.total_current = sh367601xb.tool.method.calc_current_from_adc(cur, (float)(sh367601xb.bms_system.custom_param_mgr.data.sampling_resistance / 1000000.0f));   
#if FN_PROJECT_ENABLE
                    fn_custom_data.method.fn_current_process(&sh367601xb);
#elif RZN_PROJECT_ENABLE
                    if (sh367601xb.ram[0].dsging)
                        sh367601xb.bms_system.current_mgr.data.current_state = CURRENT_STATE_DISCHARGING;
                    else if (sh367601xb.ram[0].chging)
                        sh367601xb.bms_system.current_mgr.data.current_state = CURRENT_STATE_CHARGING;
                    else 
                        sh367601xb.bms_system.current_mgr.data.current_state = CURRENT_STATE_IDLE;
#endif
                    sh367601xb.bms_system.current_mgr.methods.process_current_data(&sh367601xb.bms_system.current_mgr, sh367601xb.bms_system.current_mgr.data.total_current, sh367601xb.bms_system.current_mgr.data.current_state);
                    sh367601xb.bms_system.charge_discharge_mgr.methods.process_charge_discharge_data(&sh367601xb.bms_system.charge_discharge_mgr, sh367601xb.bms_system.current_mgr.data.total_current, sh367601xb.bms_system.current_mgr.data.current_state, sh367601xb.bms_system.voltage_mgr.data.total_voltage, sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity);
                    sh367601xb.bms_system.battery_state_mgr.methods.process_battery_state_data(&sh367601xb.bms_system.battery_state_mgr, 0, sh367601xb.bms_system.current_mgr.data.total_current, sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity, sh367601xb.bms_system.current_mgr.data.current_state);
                }
                else
                {
                    /* 解析ram */
                    sh367601xb.parser.method.parse_ram(&sh367601xb.ram[sh367601xb.chip.data.current_chip_index], &data[5]);
                    sh367601xb.bms_sync.update_realtime_data(&sh367601xb);
                    /* RAM读取完成后，检查是否需要切换到下一个芯片 */
                    sh367601xb.chip.method.handle_ram_read_chip_switching(&sh367601xb, &q, 1);
                }
                break;
            }
            /* 软件复位 */
            case 0x0B:
            {
                printf("reset\n");
                sh367601xb.chip.method.handle_reset_task_chip_switching(&sh367601xb, &q);
                sh367601xb.chip.method.handle_ram_read_chip_switching(&sh367601xb, &q, 2);
                break;
            }
            default: break;
        }
    }
}


/* 数据处理 */
void User_App_Logic(void)
{
    /* 处理蓝牙数据 */
    if (Ble.flg)
    {
        Ble.flg = false;
        User_App_Sh367601x_Ble_process(Ble.buff, Ble.len);
    }
    /* 处理串口数据 */
    if (Uart.flg)
    {
        Uart.flg = false;
        User_App_Sh367601x_Uart_process(Uart.buff, Uart.len);
    }
    
#if FN_PROJECT_ENABLE
    fn_custom_data.method.fn_special_process(&sh367601xb, &q);
#endif
    /* 队列 */
    if (clock_time_exceed(read_time, node.time * 1000))
    {
        /* 解锁 */
        sh367601x_task_lock = false;
        node.time = 0;
        if (!queue_empty(&q))
        {
            /* 加锁 */
            sh367601x_task_lock = true;
            queue_pop(&q, &node);
            switch (node.data)
            {
                case QUEUE_CURR_TASK:
                {
                    sh367601xb.comm.method.read_ram(RAM_CURRENT_ADDR_START, RAM_CURRENT_ADDR_LEN);
                    break;
                }
                case QUEUE_RAM_TASK:
                {
                    sh367601xb.comm.method.read_ram(RAM_ADDR_START, RAM_ADDR_LEN);
                    break;
                }
                case QUEUE_ROM_TASK:
                {
                    sh367601xb.comm.method.read_rom(ROM_ADDR_START, ROM_ADDR_LEN);
                    break;
                }
                case QUEUE_WRITE_ENABLE_TASK:
                { 
                    sh367601xb.comm.method.write_command();
                    break;
                }
                /* 通用ROM写入任务处理 - 处理所有QUEUE_WRITE_XX_TASK */
                case QUEUE_WRITE_01_TASK: case QUEUE_WRITE_02_TASK: case QUEUE_WRITE_03_TASK:
                case QUEUE_WRITE_04_TASK: case QUEUE_WRITE_05_TASK: case QUEUE_WRITE_06_TASK:
                case QUEUE_WRITE_07_TASK: case QUEUE_WRITE_08_TASK: case QUEUE_WRITE_09_TASK:
                case QUEUE_WRITE_0A_TASK: case QUEUE_WRITE_0B_TASK: case QUEUE_WRITE_0C_TASK:
                case QUEUE_WRITE_0D_TASK: case QUEUE_WRITE_0E_TASK: case QUEUE_WRITE_0F_TASK:
                case QUEUE_WRITE_10_TASK: case QUEUE_WRITE_11_TASK: case QUEUE_WRITE_12_TASK:
                case QUEUE_WRITE_13_TASK: case QUEUE_WRITE_14_TASK: case QUEUE_WRITE_15_TASK:
                {
                    /* 获取当前芯片索引，使用对应芯片的写入缓冲区 */
                    unsigned char chip_index = sh367601xb.chip.data.current_chip_index;
                    unsigned char addr_offset = node.data - QUEUE_WRITE_01_TASK;
                    sh367601xb.comm.method.write_data(sh367601xb.write_buff[chip_index][addr_offset], addr_offset);
                    // printf("write data: %d %d\n", sh367601xb.write_buff[chip_index][addr_offset], sh367601xb.chip.data.current_chip_index);
                    break;
                }
                case QUEUE_RESET_TASK:
                {
                    /* 使用封装的芯片切换处理函数 */
                    sh367601xb.comm.method.reset();
                    break;
                }
                default: break;
            }
        }
        read_time = clock_time();
    }
}

/**
 * @brief 从Flash中读取所有数据
 * @note 在系统初始化时调用，读取所有已保存的配置数据
 */
static void User_App_Load_Flash_Data(void)
{
    printf("Loading Flash Data...\n");

    /* 电池总容量 */
    if (Flash_Check_Data_Valid(FLASH_DATA_BATT_CAPACITY))
    {
        Flash_Read_Data(FLASH_DATA_BATT_CAPACITY, (unsigned char*)&sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity);
        printf("Battery Capacity: %d mAh\n", sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity);
    }


    /* 采样电阻 */
    if (Flash_Check_Data_Valid(FLASH_DATA_RESISTANCE))
    {
        Flash_Read_Data(FLASH_DATA_RESISTANCE, (unsigned char*)&sh367601xb.bms_system.custom_param_mgr.data.sampling_resistance);
        printf("Sampling Resistance: %d uOhm\n", sh367601xb.bms_system.custom_param_mgr.data.sampling_resistance);
    }


    /* 自耗电 */
    if (Flash_Check_Data_Valid(FLASH_DATA_SELF_CONSUMPTION))
    {
        Flash_Read_Data(FLASH_DATA_SELF_CONSUMPTION, (unsigned char*)&sh367601xb.bms_system.custom_param_mgr.data.self_consumption);
        printf("Self Consumption: %d mA\n", sh367601xb.bms_system.custom_param_mgr.data.self_consumption);
    }

    
    /* 剩余容量 */
    if (Flash_Check_Data_Valid(FLASH_DATA_SURPLUS_CAPACITY))
    {
        Flash_Read_Data(FLASH_DATA_SURPLUS_CAPACITY, (unsigned char*)&sh367601xb.bms_system.charge_discharge_mgr.data.remaining_capacity);
        printf("Surplus Capacity: %d mAh\n", sh367601xb.bms_system.charge_discharge_mgr.data.remaining_capacity);
    }


    /* 累计放电 */
    if (Flash_Check_Data_Valid(FLASH_DATA_ACC_DISCHARGE))
    {
        Flash_Read_Data(FLASH_DATA_ACC_DISCHARGE, (unsigned char*)&sh367601xb.bms_system.charge_discharge_mgr.data.accumulated_discharge);
        printf("Accumulated Discharge: %d mAh\n", sh367601xb.bms_system.charge_discharge_mgr.data.accumulated_discharge);
    }

    /* SOC */
    if (Flash_Check_Data_Valid(FLASH_DATA_SOC))
    {
        Flash_Read_Data(FLASH_DATA_SOC, (unsigned char*)&sh367601xb.bms_system.battery_state_mgr.data.soc);
        printf("SOC: %d%%\n", sh367601xb.bms_system.battery_state_mgr.data.soc);
    }


    /* 芯片数量配置 */
    if (Flash_Check_Data_Valid(FLASH_DATA_CHIP_COUNT))
    {
        unsigned char chip_count;
        Flash_Read_Data(FLASH_DATA_CHIP_COUNT, &chip_count);
        if (chip_count > 0 && chip_count <= MAX_CHIP_COUNT) {
            sh367601xb.chip.method.set_chip_count(&sh367601xb, chip_count);
            printf("Chip Count: %d (from Flash)\n", chip_count);
        } else {
            printf("Chip Count: Invalid value in Flash, using default\n");
        }
    }

    /* 芯片类型配置 */
    if (Flash_Check_Data_Valid(FLASH_DATA_CHIP_TYPE))
    {
        unsigned char chip_type;
        Flash_Read_Data(FLASH_DATA_CHIP_TYPE, &chip_type);
        sh367601xb.chip.method.set_chip_type(&sh367601xb, 0, chip_type);
        printf("Chip Type Config: %d (from Flash)\n", chip_type);
    }

#if FN_PROJECT_ENABLE
    /* FN项目专用数据 */

    /* 零点电流 */
    if (Flash_Check_Data_Valid(FLASH_FN_DATA_ZERO_CURRENT))
    {
        Flash_Read_Data(FLASH_FN_DATA_ZERO_CURRENT, (unsigned char*)&fn_custom_data.data.zero_current);
        printf("Zero Current: %d (from Flash)\n", fn_custom_data.data.zero_current);
    }
    else
    {
        fn_custom_data.data.zero_current = 0;
        printf("Zero Current: Default (0)\n");
    }

    /* 单体补偿电压 */
    if (Flash_Check_Data_Valid(FLASH_FN_DATA_VOLTAGE))
    {
        Flash_Read_Data(FLASH_FN_DATA_VOLTAGE, (unsigned char*)&fn_custom_data.data.voltage_calibration[0]);
        printf("Voltage Calibration: Loaded from Flash\n");
    }


    /* 产品序列号 */
    if (Flash_Check_Data_Valid(FLASH_FN_DATA_SERIAL_NUMBER))
    {
        Flash_Read_Data(FLASH_FN_DATA_SERIAL_NUMBER, (unsigned char*)&fn_custom_data.data.factory_info[0]);
        printf("Serial Number: %.30s\n", fn_custom_data.data.factory_info);
    }


    /* 出厂信息 */
    if (Flash_Check_Data_Valid(FLASH_FN_DATA_EX_FACTORY))
    {
        Flash_Read_Data(FLASH_FN_DATA_EX_FACTORY, (unsigned char*)&fn_custom_data.data.production_date[0]);
        printf("Factory Info: Loaded from Flash\n");
    }

#endif
}

