# BMS系统文字架构图

## 1. 系统总体架构图

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  BMS电池管理系统                                  │
│                              (Battery Management System)                        │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                   应用层                                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                │
│  │   SOC计算模块   │  │   SOH计算模块   │  │   保护逻辑模块  │                │
│  │                 │  │                 │  │                 │                │
│  │ • 库仑计法      │  │ • 健康度评估    │  │ • 过压保护      │                │
│  │ • 开路电压法    │  │ • 容量衰减      │  │ • 欠压保护      │                │
│  │ • 卡尔曼滤波    │  │ • 内阻增长      │  │ • 过流保护      │                │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  定制化层                                       │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                        FN_CustomDataManager                                │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │ │
│  │  │   出厂信息管理  │  │   电压校准管理  │  │   零点电流校准  │            │ │
│  │  │                 │  │                 │  │                 │            │ │
│  │  │ • 30字节出厂信息│  │ • 64路电压校准  │  │ • 零点采集      │            │ │
│  │  │ • 8字节生产日期 │  │ • 校准系数存储  │  │ • 平均值计算    │            │ │
│  │  │ • 序列号管理    │  │ • 实时校准应用  │  │ • Flash存储     │            │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘            │ │
│  │  ┌─────────────────────────────────────────────────────────────────────────┐ │ │
│  │  │                        特殊处理逻辑模块                                │ │ │
│  │  │ • fn_Rs2058_Init()        - RS2058芯片初始化                          │ │ │
│  │  │ • fn_Rs2058_Gpio1_Low()   - GPIO1拉低控制                             │ │ │
│  │  │ • fn_Rs2058_Gpio1_High()  - GPIO1拉高控制                             │ │ │
│  │  │ • fn_special_process()    - 特殊业务逻辑处理                          │ │ │
│  │  │ • fn_current_process()    - 电流特殊处理                              │ │ │
│  │  └─────────────────────────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                               BMS数据管理层                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                           BMS_DataManager                                  │ │
│  │                                                                             │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │ │
│  │  │  AlarmManager   │  │  StatusManager  │  │ VoltageManager  │            │ │
│  │  │   (报警管理)    │  │   (状态管理)   │  │   (电压管理)    │            │ │
│  │  │                 │  │                 │  │                 │            │ │
│  │  │ • 充电高温保护  │  │ • 均衡状态      │  │ • 总电压        │            │ │
│  │  │ • 充电低温保护  │  │ • 充电MOS       │  │ • 单体电压[64]  │            │ │
│  │  │ • 放电高温保护  │  │ • 放电MOS       │  │ • 最大/最小电压 │            │ │
│  │  │ • 放电低温保护  │  │ • 充电状态      │  │ • 平均电压      │            │ │
│  │  │ • 充电过流      │  │ • 放电状态      │  │ • 电池压差      │            │ │
│  │  │ • 放电过流1/2   │  │                 │  │ • 电池串数      │            │ │
│  │  │ • 过压/欠压     │  │                 │  │                 │            │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘            │ │
│  │                                                                             │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │ │
│  │  │ CurrentManager  │  │TemperatureManager│ │ChargeDischargeManager│       │ │
│  │  │   (电流管理)    │  │   (温度管理)    │  │  (充放电管理)   │            │ │
│  │  │                 │  │                 │  │                 │            │ │
│  │  │ • 总电流        │  │ • 外部温度[10]  │  │ • 循环次数      │            │ │
│  │  │ • 最大充电电流  │  │ • 最大外部温度  │  │ • 剩余容量      │            │ │
│  │  │ • 最大放电电流  │  │ • 最小外部温度  │  │ • 剩余时间      │            │ │
│  │  │ • 电流状态      │  │ • 芯片温度      │  │ • 累计充电量    │            │ │
│  │  │   (空闲/充/放)  │  │ • MOS温度       │  │ • 累计放电量    │            │ │
│  │  │                 │  │ • 温度传感器数  │  │ • 当前功率      │            │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘            │ │
│  │                                                                             │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │ │
│  │  │BatteryStateManager│ProtectionParameterManager│CustomParameterManager│  │ │
│  │  │ (电池状态管理)  │  │ (保护参数管理)  │  │ (自定义参数管理)│            │ │
│  │  │                 │  │                 │  │                 │            │ │
│  │  │ • SOC电量百分比 │  │ • 过压保护阈值  │  │ • 电池总容量    │            │ │
│  │  │ • SOH健康状态   │  │ • 欠压保护阈值  │  │ • 电池剩余容量  │            │ │
│  │  │ • 上次采样时间  │  │ • 过流保护阈值  │  │ • 采样电阻      │            │ │
│  │  │ • 当前采样时间  │  │ • 温度保护阈值  │  │ • 自耗电        │            │ │
│  │  │                 │  │ • 各种延时设置  │  │                 │            │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘            │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                               硬件抽象层                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                          SH367601B_Device                                  │ │
│  │                      (继承BMS数据管理系统)                                  │ │
│  │                                                                             │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │ │
│  │  │SH367601B_Config │  │SH367601B_Communication│SH367601B_Parser│          │ │
│  │  │  (配置管理)     │  │   (通信驱动)    │  │  (数据解析)     │            │ │
│  │  │                 │  │                 │  │                 │            │ │
│  │  │ • set_id()      │  │ • reset()       │  │ • parse_rom()   │            │ │
│  │  │ • set_eovr()    │  │ • write_command()│ │ • parse_ram()   │            │ │
│  │  │ • set_euvr()    │  │ • write_data()  │  │ • print_rom()   │            │ │
│  │  │ • set_ocra()    │  │ • read_rom()    │  │ • print_ram()   │            │ │
│  │  │ • set_bals()    │  │ • read_ram()    │  │ • pack_rom_data()│           │ │
│  │  │ • pack_rom_data()│ │                 │  │                 │            │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘            │ │
│  │                                                                             │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │ │
│  │  │SH367601B_Converter│SH367601B_Tool   │  │ SH367601B_Chip  │            │ │
│  │  │  (数据转换)     │  │  (工具函数)     │  │  (芯片管理)     │            │ │
│  │  │                 │  │                 │  │                 │            │ │
│  │  │ • ov_to_voltage()│ │ • calc_voltage_from_adc()│ • 芯片类型[4]   │       │ │
│  │  │ • uv_to_voltage()│ │ • calc_current_from_adc()│ • 芯片数量      │       │ │
│  │  │ • ocd_to_voltage()│ │ • calc_external_temp()  │ • 当前芯片索引  │       │ │
│  │  │ • tc_to_delay() │  │ • calc_internal_temp()  │ • 写ROM进度标志 │       │ │
│  │  │ • temp_to_value()│ │ • ntc_calculate_temp()  │ • 芯片切换函数  │       │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘            │ │
│  │                                                                             │ │
│  │  ┌─────────────────────────────────────────────────────────────────────────┐ │ │
│  │  │                          寄存器数据存储                                │ │ │
│  │  │                                                                         │ │ │
│  │  │  ┌─────────────────────────┐    ┌─────────────────────────┐            │ │ │
│  │  │  │    ROM寄存器数据[4]     │    │    RAM寄存器数据[4]     │            │ │ │
│  │  │  │     (配置参数)          │    │     (实时数据)          │            │ │ │
│  │  │  │                         │    │                         │            │ │ │
│  │  │  │ • 地址: 0x00-0x15      │    │ • 地址: 0x40-0x6E      │            │ │ │
│  │  │  │ • 长度: 21字节          │    │ • 长度: 46字节          │            │ │ │
│  │  │  │ • 过压保护设置          │    │ • 16路电芯电压          │            │ │ │
│  │  │  │ • 欠压保护设置          │    │ • 电流ADC值             │            │ │ │
│  │  │  │ • 过流保护设置          │    │ • 温度ADC值             │            │ │ │
│  │  │  │ • 温度保护设置          │    │ • 状态标志位            │            │ │ │
│  │  │  │ • 均衡设置              │    │ • 报警标志位            │            │ │ │
│  │  │  │ • 延时设置              │    │                         │            │ │ │
│  │  │  └─────────────────────────┘    └─────────────────────────┘            │ │ │
│  │  └─────────────────────────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  工具层                                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                │
│  │   SOC计算工具   │  │   数字滤波工具  │  │  NTC温度转换    │                │
│  │                 │  │                 │  │      工具       │                │
│  │ • soc_compute() │  │ • hybridFilter()│  │ • ntc_calculate_temp_from_adc()│ │
│  │   库仑计法      │  │   SMA+EMA混合   │  │   查表法转换    │                │
│  │   SOC计算       │  │   滤波算法      │  │   温度偏移校准  │                │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                │
└─────────────────────────────────────────────────────────────────────────────────┘
                                        │
                                        ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                                  硬件层                                         │
│  ┌─────────────────────────────────────────────────────────────────────────────┐ │
│  │                            SH367601B芯片                                   │ │
│  │                        (支持10串/14串/16串)                                │ │
│  │                                                                             │ │
│  │  ┌─────────────────────────┐              ┌─────────────────────────┐      │ │
│  │  │      ROM寄存器          │              │      RAM寄存器          │      │ │
│  │  │    (配置参数存储)       │              │    (实时数据存储)       │      │ │
│  │  │                         │              │                         │      │ │
│  │  │ 地址范围: 0x00-0x15     │              │ 地址范围: 0x40-0x6E     │      │ │
│  │  │ 总长度: 21字节          │              │ 总长度: 46字节          │      │ │
│  │  │                         │              │                         │      │ │
│  │  │ 0x00: ID设置            │              │ 0x40-0x5F: 电芯电压     │      │ │
│  │  │ 0x01: 过压保护(EOVR)    │              │ 0x60-0x65: 温度数据     │      │ │
│  │  │ 0x02: 欠压保护(EUVR)    │              │ 0x66-0x67: 电流数据     │      │ │
│  │  │ 0x03: 过流保护(OCRA)    │              │ 0x68-0x6D: 状态标志     │      │ │
│  │  │ 0x04-0x14: 其他配置     │              │ 0x6E: 报警标志          │      │ │
│  │  └─────────────────────────┘              └─────────────────────────┘      │ │
│  └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 2. 数据流向图

```
数据输入流:
硬件传感器 → SH367601B芯片 → RAM寄存器 → 通信读取 → 数据解析 → 数据转换 → 管理器处理 → 应用层

配置输出流:
应用层配置 → 参数验证 → 数据转换 → ROM寄存器打包 → 通信写入 → SH367601B芯片 → 硬件执行

定制化处理流:
标准数据 → FN定制管理器 → 特殊处理逻辑 → 校准应用 → 最终数据输出
```

## 3. 模块依赖关系图

```
BMS_DataManager (核心)
    ├── AlarmManager (报警管理)
    ├── StatusManager (状态管理)
    ├── VoltageManager (电压管理)
    ├── CurrentManager (电流管理)
    ├── TemperatureManager (温度管理)
    ├── ChargeDischargeManager (充放电管理)
    ├── BatteryStateManager (电池状态管理)
    ├── ProtectionParameterManager (保护参数管理)
    └── CustomParameterManager (自定义参数管理)

SH367601B_Device (硬件抽象)
    ├── BMS_DataManager (继承)
    ├── SH367601B_Config (配置管理)
    ├── SH367601B_Communication (通信驱动)
    ├── SH367601B_Parser (数据解析)
    ├── SH367601B_Converter (数据转换)
    ├── SH367601B_Tool (工具函数)
    ├── SH367601B_Chip (芯片管理)
    ├── ROM寄存器数据[MAX_CHIP_COUNT]
    └── RAM寄存器数据[MAX_CHIP_COUNT]

FN_CustomDataManager (定制化)
    ├── 出厂信息管理 (30字节)
    ├── 生产日期管理 (8字节)
    ├── 电压校准管理 (64路)
    ├── 零点电流校准
    └── 特殊处理方法集合

工具函数模块
    ├── SOC计算工具 (soc_compute)
    ├── 数字滤波工具 (hybridFilter)
    └── NTC温度转换工具 (ntc_calculate_temp_from_adc)
```

## 4. 使用模型版本
Claude Sonnet 4
