#include "rzn_agreement.h"
#include "../../user_app_main.h"
#include "../../bms/zy/sh367601xb.h"


#if RZN_PROJECT_ENABLE
/* 外部引用 */
extern SH367601B_Device sh367601xb;
extern Queue q;

/**
 * @brief      Modbus CRC16计算函数
 * @param[in]  data - 需要计算CRC的数据
 * @param[in]  len - 数据长度
 * @return     计算得到的CRC16校验值
 */
static unsigned short crc16_modbus(unsigned char *data, unsigned int len)
{
    unsigned short crc = 0xFFFF;
    
    for (unsigned int i = 0; i < len; i++)
    {
        crc ^= data[i];
        for (unsigned int j = 0; j < 8; j++)
        {
            if (crc & 0x0001)
            {
                crc >>= 1;
                crc ^= 0xA001;
            }
            else
            {
                crc >>= 1;
            }
        }
    }
    
    return crc;
}
/**
 * @brief 蓝牙协议数据处理
 */
void bluetooth_protocol_process(unsigned char *data, unsigned short len)
{
    if (0xE2 == data[0] && len >= 4)
    {
        u8 cmd[50] = {0};
        switch (data[1])
        {
            /* 读 */
            case 0x03:
            {   
                u16 addr = (data[2] << 8) | data[3];
                u16 length = (data[4] << 8) | data[5];
                
                /* 准备响应数据 */
                cmd[0] = 0xE2;
                cmd[1] = 0x03;
                cmd[2] = length * 2;  /* 数据长度 */
                
                unsigned short i;
                for (i = 0; i < length; i++) 
                {
                    u16 reg_addr = addr + i;
                    u16 reg_value = 0;
                    
                    switch (reg_addr) 
                    {
                        /* 0x01: 实时信息 - SOC */
                        case 0x01:
                            reg_value = sh367601xb.bms_system.battery_state_mgr.data.soc;
                            break;
                        /* 0x02: 实时信息 - SOH */
                        case 0x02:
                            reg_value = sh367601xb.bms_system.battery_state_mgr.data.soh / 100;
                            break;
                        /* 0x03: 实时信息 - 总电压 */
                        case 0x03:
                            reg_value = sh367601xb.bms_system.voltage_mgr.data.total_voltage / 10;
                            break;
                        /* 0x04: 实时信息 - 总电流 */
                        case 0x04:
                            reg_value = sh367601xb.bms_system.current_mgr.data.total_current / 10;
                            break;
                        /* 0x05: 实时信息 - 充电循环次数 */
                        case 0x05:
                            reg_value = 0; /* 循环次数暂未实现 */
                            break;
                        /* 0x06: 实时信息 - 电池容量 */
                        case 0x06:
                            reg_value = sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity / 10;
                            break;
                        /* 0x07: 实时信息 - 平均电压 */
                        case 0x07:
                            reg_value = sh367601xb.bms_system.voltage_mgr.data.average_voltage;
                            break;
                        /* 0x08: 实时信息 - 最高单体电压 */
                        case 0x08:
                            reg_value = sh367601xb.bms_system.voltage_mgr.data.max_voltage;
                            break;
                        /* 0x09: 实时信息 - 最低单体电压 */
                        case 0x09:
                            reg_value = sh367601xb.bms_system.voltage_mgr.data.min_voltage;
                            break;
                        /* 0x0A: 实时信息 - MOS温度 */
                        case 0x0A:
                            reg_value = 0x00; /* 当前未使用 */
                            break;
                        /* 0x0B: 实时信息 - 最高温度 */
                        case 0x0B:
                            reg_value = sh367601xb.bms_system.temp_mgr.data.max_external_temp;
                            break;
                        /* 0x0C: 实时信息 - 最低温度 */
                        case 0x0C:
                            reg_value = sh367601xb.bms_system.temp_mgr.data.min_external_temp;
                            break;
                        /* 0x0D: 实时信息 - 硬件版本 */
                        case 0x0D:
                            reg_value = 0x0100; /* 硬件版本v1.0 */
                            break;
                        /* 0x0E: 实时信息 - 软件版本 */
                        case 0x0E:
                            reg_value = 0x0100; /* 软件版本v1.0 */
                            break;
                        /* 0x0F: 实时信息 - 电池串数 */
                        case 0x0F:
                            reg_value = sh367601xb.bms_system.voltage_mgr.data.battery_count;
                            break;
                        /* 0x10: 实时信息 - 温度数量 */
                        case 0x10:
                            reg_value = sh367601xb.bms_system.temp_mgr.data.external_temp_count;
                            break;

                        /* 0x71-0x73: 容量串数设置 */
                        case 0x70:
                            Flash_Read_Data(FLASH_DATA_BATT_CAPACITY, (unsigned char *)&sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity);
                            reg_value =  sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity / 10;
                            break;
                        case 0x71:
                            Flash_Read_Data(FLASH_DATA_SURPLUS_CAPACITY, (unsigned char *)&sh367601xb.bms_system.custom_param_mgr.data.battery_remaining_capacity);
                            reg_value = sh367601xb.bms_system.custom_param_mgr.data.battery_remaining_capacity / 10;
                            break;
                        case 0x72:
                            reg_value = sh367601xb.bms_system.voltage_mgr.data.battery_count;
                            break;
                            
                        /* 0x80-0x89: 温度相关设置 */
                        case 0x80:
                            reg_value = 0;
                            break;
                        case 0x81:
                            reg_value = 0;
                            break;
                        case 0x82:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.charge_high_temp_protection;
                            break;
                        case 0x83:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.charge_high_temp_recovery;
                            break;
                        case 0x84:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.charge_low_temp_protection;
                            break;
                        case 0x85:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.charge_low_temp_recovery;
                            break;
                        case 0x86:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.discharge_high_temp_protection;
                            break;
                        case 0x87:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.discharge_high_temp_recovery;
                            break;
                        case 0x88:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.discharge_low_temp_recovery;
                            break;
                        case 0x89:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.discharge_low_temp_recovery;
                            break;
                            
                        /* 0x90-0x95: 电压相关设置 */
                        case 0x90:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.overvoltage_protection;
                            break;
                        case 0x91:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.overvoltage_recovery;
                            break;
                        case 0x92:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.overvoltage_delay;
                            break;
                        case 0x93:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.undervoltage_protection;
                            break;
                        case 0x94:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.undervoltage_recovery;
                            break;
                        case 0x95:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.undervoltage_delay;
                            break;
                            
                        /* 0xA0-0xAA: 其他设置 */
                        case 0xA0:
                            /* 休眠唤醒电流 - 暂未实现 */
                            reg_value = 0;
                            break;
                        case 0xA1:
                            /* 休眠延时 - 暂未实现 */
                            reg_value = 0;
                            break;
                        case 0xA2:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.balance_start_voltage;
                            break;
                        case 0xA3:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.balance_start_voltage_diff;
                            break;
                        case 0xA4:
                            Flash_Read_Data(FLASH_DATA_SELF_CONSUMPTION, (unsigned char *)&sh367601xb.bms_system.custom_param_mgr.data.self_consumption);
                            reg_value = sh367601xb.bms_system.custom_param_mgr.data.self_consumption;
                            break;
                        case 0xA5:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.charge_overcurrent1;
                            break;
                        case 0xA6:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.charge_overcurrent1_delay;
                            break;
                        case 0xA7:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.discharge_overcurrent1;
                            break;
                        case 0xA8:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.discharge_overcurrent1_delay;
                            break;
                        case 0xA9:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.discharge_overcurrent2;
                            break;
                        case 0xAA:
                            reg_value = sh367601xb.bms_system.protection_param_mgr.data.discharge_overcurrent2_delay;
                            break;
                            
                        /* 单体电压信息 */
                        case 0x100:
                            reg_value = sh367601xb.bms_system.voltage_mgr.data.battery_count;
                            break;
                        case 0x101:
                        case 0x102:
                        case 0x103:
                        case 0x104:
                        case 0x105:
                        case 0x106:
                        case 0x107:
                        case 0x108:
                        case 0x109:
                        case 0x10A:
                        case 0x10B:
                        case 0x10C:
                        case 0x10D:
                        case 0x10E:
                        case 0x10F:
                        case 0x110:
                            reg_value = sh367601xb.bms_system.voltage_mgr.data.cell_voltages[reg_addr - 0x101];
                            break;
                        /* 温度信息 */
                        case 0x200:
                            reg_value = sh367601xb.bms_system.temp_mgr.data.external_temp_count;
                            break;
                        case 0x201:
                        case 0x202:
                        case 0x203:
                            reg_value = sh367601xb.bms_system.temp_mgr.data.external_temp[reg_addr - 0x201];
                            break;
                            
                        /* 告警状态 */
                        case 0x300:
                            reg_value = (sh367601xb.bms_system.alarm_mgr.data.charge_over_temp << 0) |
                                       (sh367601xb.bms_system.alarm_mgr.data.charge_under_temp << 1) |
                                       (sh367601xb.bms_system.alarm_mgr.data.discharge_over_temp << 2) |
                                       (sh367601xb.bms_system.alarm_mgr.data.discharge_under_temp << 3) |
                                       (sh367601xb.bms_system.alarm_mgr.data.charge_overcurrent << 4) |
                                       (sh367601xb.bms_system.alarm_mgr.data.discharge_overcurrent1 << 5) |
                                       (sh367601xb.bms_system.alarm_mgr.data.discharge_overcurrent2 << 6) |
                                       (sh367601xb.bms_system.alarm_mgr.data.undervoltage << 7);
                            break;
                        case 0x330:
                            reg_value = (sh367601xb.bms_system.status_mgr.data.balance_status << 0) |
                                       (sh367601xb.bms_system.status_mgr.data.charge_mos << 1) |
                                       (sh367601xb.bms_system.status_mgr.data.discharge_mos << 2) |
                                       (sh367601xb.bms_system.status_mgr.data.charge_status << 3) |
                                       (sh367601xb.bms_system.status_mgr.data.discharge_status << 4);
                            break;
                        default:
                            reg_value = 0xFFFF; /* 未知寄存器返回0xFFFF */
                            break;
                    }
                    
                    /* 写入响应数据 */
                    cmd[3 + i*2] = reg_value >> 8;
                    cmd[4 + i*2] = reg_value & 0xFF;
                }
                
                /* 计算CRC */
                unsigned short crc = crc16_modbus(cmd, 3 + length*2);
                cmd[3 + length*2] = crc >> 8;
                cmd[4 + length*2] = crc & 0xFF;
                
                /* 发送响应 */
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, 5 + length*2);
                break;
            }
            /* 写单个 */
            case 0x06:      
            {
                u16 addr = (data[2] << 8) | data[3];
                // u16 length = (data[4] << 8) | data[5];
                u8 i;
                
                /* 保存当前ROM配置 */
                sh367601xb.config.method.create_rom_copy(&sh367601xb);
                
                /* 根据地址和长度写入数据 */
                for (i = 0; i < 1; i++) {
                    u16 reg_addr = addr + i;
                    u16 reg_data = (data[4 + i*2] << 8) | data[5 + i*2];
                    
                    switch (reg_addr) {
                        /* MOS高温保护设置 */
                        case 0x80:
                            /* 这里保留供后续实现 */
                            break;
                        /* MOS高温恢复 */
                        case 0x81:
                            /* 这里保留供后续实现 */
                            break;
                        /* 充电高温 */
                        case 0x82:
                            sh367601xb.config.method.set_otc(&sh367601xb, reg_data);
                            break;
                        /* 充电高温恢复 */
                        case 0x83:
                            sh367601xb.config.method.set_otcr(&sh367601xb, reg_data);
                            break;
                        /* 充电低温 */
                        case 0x84:
                            sh367601xb.config.method.set_utc(&sh367601xb, reg_data);
                            break;
                        /* 充电低温恢复 */
                        case 0x85:
                            sh367601xb.config.method.set_utcr(&sh367601xb, reg_data);
                            break;
                        /* 放电高温 */
                        case 0x86:
                            sh367601xb.config.method.set_otd(&sh367601xb, reg_data);
                            break;
                        /* 放电高温恢复 */
                        case 0x87:
                            sh367601xb.config.method.set_otdr(&sh367601xb, reg_data);
                            break;
                        /* 放电低温 */
                        case 0x88:
                            sh367601xb.config.method.set_utd(&sh367601xb, reg_data);
                            break;
                        /* 放电低温恢复 */
                        case 0x89:
                            sh367601xb.config.method.set_utdr(&sh367601xb, reg_data);
                            break;
                        /* 过压保护电压 */
                        case 0x90:
                            sh367601xb.config.method.set_ov(&sh367601xb, reg_data);
                            break;
                        /* 过压恢复电压 */
                        case 0x91:
                            sh367601xb.config.method.set_ovr(&sh367601xb, reg_data);
                            break;
                        /* 过压保护延时 */
                        case 0x92:
                            sh367601xb.config.method.set_ovt(&sh367601xb, reg_data);
                            break;
                        /* 欠压保护电压 */
                        case 0x93:
                            sh367601xb.config.method.set_uv(&sh367601xb, reg_data);
                            break;
                        /* 欠压恢复电压 */
                        case 0x94:
                            sh367601xb.config.method.set_uvr(&sh367601xb, reg_data);
                            break;
                        /* 欠压保护延时 */
                        case 0x95:
                            sh367601xb.config.method.set_uvt(&sh367601xb, reg_data);
                            break;
                        /* 休眠唤醒电流 */
                        case 0xA0:
                            /* 这里保留供后续实现 */
                            break;
                        /* 休眠延时 */
                        case 0xA1:
                            /* 这里保留供后续实现 */
                            break;
                        /* 平衡启动电压 */
                        case 0xA2:
                            sh367601xb.config.method.set_balv(&sh367601xb, reg_data);
                            break;
                        /* 平衡延时 */
                        case 0xA3:
                            sh367601xb.config.method.set_bald(&sh367601xb, reg_data);
                            break;
                        /* 自耗电设置 */
                        case 0xA4:
                        {
                            /* 保存自耗电到自定义参数管理器 */
                            sh367601xb.bms_system.custom_param_mgr.data.self_consumption = reg_data;
                            Flash_Write_Data(FLASH_DATA_SELF_CONSUMPTION, (unsigned char *)&reg_data);
                            break;
                        }
                        /* 充电过流设置 */
                        case 0xA5:
                            sh367601xb.config.method.set_occv(&sh367601xb, reg_data);
                            break;
                        /* 充电过流延时 */
                        case 0xA6:
                            sh367601xb.config.method.set_occt(&sh367601xb, reg_data);
                            break;
                        /* 放电一级过流 */
                        case 0xA7:
                            sh367601xb.config.method.set_ocd1v(&sh367601xb, reg_data);
                            break;
                        /* 一级过流延时 */
                        case 0xA8:
                            sh367601xb.config.method.set_ocd1t(&sh367601xb, reg_data);
                            break;
                        /* 放电二级过流 */
                        case 0xA9:
                            sh367601xb.config.method.set_ocd2v(&sh367601xb, reg_data);
                            break;
                        /* 二级过流延时 */
                        case 0xAA:
                            sh367601xb.config.method.set_ocd2t(&sh367601xb, reg_data);
                            break;
                        /* 电池容量设置 */
                        case 0x70:
                        {
                            /* 保存电池总容量到自定义参数管理器 */
                            sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity = reg_data * 10;
                            Flash_Write_Data(FLASH_DATA_BATT_CAPACITY, (unsigned char *)&sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity);
                            break;
                        }
                        /* 剩余容量设置 */
                        case 0x71:
                        {
                            /* 保存剩余容量到自定义参数管理器 */
                            sh367601xb.bms_system.custom_param_mgr.data.battery_remaining_capacity = reg_data * 10;
                            Flash_Write_Data(FLASH_DATA_SURPLUS_CAPACITY, (unsigned char *)&sh367601xb.bms_system.custom_param_mgr.data.battery_remaining_capacity);
                            sh367601xb.bms_system.battery_state_mgr.data.soc = ((sh367601xb.bms_system.custom_param_mgr.data.battery_remaining_capacity * 10000.0) / sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity);
                            break;
                        }
                        /* 电池串数设置 */
                        case 0x72:
                        {
                            unsigned char battery_count = reg_data > 16 ? 16 : reg_data;
                            sh367601xb.config.method.set_cn(&sh367601xb, 0, battery_count);
                            break;
                        }
                        default:
                            /* 未知寄存器，跳过 */
                            break;
                    }
                }
                
                /* 将配置写入EEPROM */
                sh367601xb.config.method.pack_rom_data(sh367601xb.write_buff[0], &sh367601xb.write_rom);
                sh367601xb.chip.method.start_batch_write_rom(&sh367601xb, &q);
                
                /* 响应确认 */
                memcpy(&cmd[0], &data[0], 6);
                unsigned short crc = crc16_modbus(cmd, 6);
                cmd[6] = crc >> 8;
                cmd[7] = crc & 0xFF;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, 8);
                break;
            }
            /* 写寄存器 */
            case 0x10:      
            {
                u16 addr = (data[2] << 8) | data[3];
                u16 length = (data[4] << 8) | data[5];
                u8 i;
                
                /* 保存当前ROM配置 */
                sh367601xb.config.method.create_rom_copy(&sh367601xb);
                
                /* 根据地址和长度写入数据 */
                for (i = 0; i < length; i++) 
                {
                    u16 reg_addr = addr + i;
                    u16 reg_data = (data[6 + i*2] << 8) | data[7 + i*2];
                    
                    switch (reg_addr) {
                        /* MOS高温保护设置 */
                        case 0x80:
                            /* 这里保留供后续实现 */
                            break;
                        /* MOS高温恢复 */
                        case 0x81:
                            /* 这里保留供后续实现 */
                            break;
                        /* 充电高温 */
                        case 0x82:
                            sh367601xb.config.method.set_otc(&sh367601xb, reg_data);
                            break;
                        /* 充电高温恢复 */
                        case 0x83:
                            sh367601xb.config.method.set_otcr(&sh367601xb, reg_data);
                            break;
                        /* 充电低温 */
                        case 0x84:
                            sh367601xb.config.method.set_utc(&sh367601xb, reg_data);
                            break;
                        /* 充电低温恢复 */
                        case 0x85:
                            sh367601xb.config.method.set_utcr(&sh367601xb, reg_data);
                            break;
                        /* 放电高温 */
                        case 0x86:
                            sh367601xb.config.method.set_otd(&sh367601xb, reg_data);
                            break;
                        /* 放电高温恢复 */
                        case 0x87:
                            sh367601xb.config.method.set_otdr(&sh367601xb, reg_data);
                            break;
                        /* 放电低温 */
                        case 0x88:
                            sh367601xb.config.method.set_utd(&sh367601xb, reg_data);
                            break;
                        /* 放电低温恢复 */
                        case 0x89:
                            sh367601xb.config.method.set_utdr(&sh367601xb, reg_data);
                            break;
                        /* 过压保护电压 */
                        case 0x90:
                            sh367601xb.config.method.set_ov(&sh367601xb, reg_data);
                            break;
                        /* 过压保护延时 */
                        case 0x91:
                            sh367601xb.config.method.set_ovt(&sh367601xb, reg_data);
                            break;
                        /* 过压恢复电压 */
                        case 0x92:
                            sh367601xb.config.method.set_ovr(&sh367601xb, reg_data);
                            break;
                        /* 欠压保护电压 */
                        case 0x93:
                            sh367601xb.config.method.set_uv(&sh367601xb, reg_data);
                            break;
                        /* 欠压恢复电压 */
                        case 0x94:
                            sh367601xb.config.method.set_uvr(&sh367601xb, reg_data);
                            break;
                        /* 欠压保护延时 */
                        case 0x95:
                            sh367601xb.config.method.set_uvt(&sh367601xb, reg_data);
                            break;
                        /* 休眠唤醒电流 */
                        case 0xA0:
                            /* 这里保留供后续实现 */
                            break;
                        /* 休眠延时 */
                        case 0xA1:
                            /* 这里保留供后续实现 */
                            break;
                        /* 平衡启动电压 */
                        case 0xA2:
                            sh367601xb.config.method.set_balv(&sh367601xb, reg_data);
                            break;
                        /* 平衡延时 */
                        case 0xA3:
                            sh367601xb.config.method.set_bald(&sh367601xb, reg_data);
                            break;
                        /* 零点电流设置 */
                        case 0xA4:
                        {
                            /* 保存零点电流到自定义参数管理器 */
                            sh367601xb.bms_system.custom_param_mgr.data.self_consumption = reg_data;
                            break;
                        }
                        /* 充电过流设置 */
                        case 0xA5:
                            sh367601xb.config.method.set_occv(&sh367601xb, reg_data);
                            break;
                        /* 充电过流延时 */
                        case 0xA6:
                            sh367601xb.config.method.set_occt(&sh367601xb, reg_data);
                            break;
                        /* 放电一级过流 */
                        case 0xA7:
                            sh367601xb.config.method.set_ocd1v(&sh367601xb, reg_data);
                            break;
                        /* 一级过流延时 */
                        case 0xA8:
                            sh367601xb.config.method.set_ocd1t(&sh367601xb, reg_data);
                            break;
                        /* 放电二级过流 */
                        case 0xA9:
                            sh367601xb.config.method.set_ocd2v(&sh367601xb, reg_data);
                            break;
                        /* 二级过流延时 */
                        case 0xAA:
                            sh367601xb.config.method.set_ocd2t(&sh367601xb, reg_data);
                            break;
                        /* 电池容量设置 */
                        case 0x70:
                        {
                            /* 保存电池总容量到自定义参数管理器 */
                            sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity = reg_data * 10;
                            break;
                        }
                        /* 剩余容量设置 */
                        case 0x71:
                        {
                            /* 保存剩余容量到自定义参数管理器 */
                            sh367601xb.bms_system.custom_param_mgr.data.battery_remaining_capacity = reg_data * 10;
                            break;
                        }
                        /* 电池串数设置 */
                        case 0x72:
                        {
                            unsigned char battery_count = reg_data > 16 ? 16 : reg_data;
                            sh367601xb.config.method.set_cn(&sh367601xb, 0, battery_count);
                            break;
                        }
                        default:
                            /* 未知寄存器，跳过 */
                            break;
                    }
                }
                
                /* 将配置写入EEPROM */
                sh367601xb.config.method.pack_rom_data(sh367601xb.write_buff[0], &sh367601xb.write_rom);
                sh367601xb.chip.method.start_batch_write_rom(&sh367601xb, &q);
                
                /* 响应确认 */
                memcpy(&cmd[0], &data[0], 6);
                unsigned short crc = crc16_modbus(cmd, 6);
                cmd[6] = crc >> 8;
                cmd[7] = crc & 0xFF;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, 8);
                break;
            }
            default:
            {
                break;
            }
        }
    }
} 
#endif