#include "tl_common.h"
#include "sh367601xb_converter.h"

/* ==========================================数据转换模块实现========================================== */

/**
 * @brief 过充电保护电压寄存器值转换为实际电压
 * @param ov 过充电保护电压寄存器值
 * @return 实际电压值，单位：mV
 */
unsigned short sh367601b_converter_ov_to_voltage(unsigned short ov)
{
    return ov * 5;
}

/**
 * @brief 过充电恢复电压寄存器值转换为实际电压
 * @param ovr 过充电恢复电压寄存器值
 * @return 实际电压值，单位：mV
 */
unsigned short sh367601b_converter_ovr_to_voltage(unsigned short ovr)
{
    return ovr * 10;
}

/**
 * @brief 过充电保护延时寄存器值转换 s
 * @param ovr 过充电保护延时寄存器
 * @return 下标值
 */
unsigned short sh367601b_converter_ovt_to_value(unsigned char ovt)
{
    unsigned short ovt_table[] = {20, 100, 200, 500};
    if (ovt > sizeof(ovt_table)) return 0;
    return ovt_table[ovt];
}

/**
 * @brief 过放电保护延时寄存器值转换 s
 * @param uvt 过放电保护延时寄存器
 * @return 下标值
 */
unsigned short sh367601b_converter_uvt_to_value(unsigned char uvt)
{
    unsigned short uvt_table[] = {50, 100, 200, 500};
    if (uvt > sizeof(uvt_table)) return 0;
    return uvt_table[uvt];
}

/**
 * @brief 放电过流1保护延时寄存器值转换 s
 * @param ovr 放电过流1保护延时寄存器
 * @return 下标值
 */
unsigned short sh367601b_converter_ocd1t_to_value(unsigned char ocd1t)
{
    unsigned short ocd1t_table[] = {50, 100, 200, 500};
    if (ocd1t > sizeof(ocd1t_table)) return 0;
    return ocd1t_table[ocd1t];
}

/**
 * @brief 放电过流2保护延时寄存器值转换 ms
 * @param ovr 放电过流2保护延时寄存器
 * @return 下标值
 */
unsigned short sh367601b_converter_ocd2t_to_value(unsigned char ocd2t)
{
    unsigned short ocd2t_table[] = {50, 100, 200, 500};
    if (ocd2t > sizeof(ocd2t_table)) return 0;
    return ocd2t_table[ocd2t];
}

/**
 * @brief 充电过流保护延时寄存器值转换 s
 * @param ovr 充电过流保护延时寄存器
 * @return 下标值
 */
unsigned short sh367601b_converter_occt_to_value(unsigned char occt)
{
    unsigned short occt_table[] = {10, 100, 300, 1000};
    if (occt > sizeof(occt_table)) return 0;
    return occt_table[occt];
}

/**
 * @brief 均衡进入延时寄存器值转换 s
 * @param ovr 均衡进入延时寄存器
 * @return 下标值
 */
unsigned short sh367601b_converter_balt_to_value(unsigned char balt)
{
    unsigned short balt_table[] = {5, 50};
    if (balt > sizeof(balt_table)) return 0;
    return balt_table[balt];
}

/**
 * @brief 均衡开启压差 mv
 * @param ovr 均衡进入延时寄存器
 * @return 下标值
 */
unsigned short sh367601b_converter_bald_to_value(unsigned char bald)
{
    unsigned short bald_table[] = {0, 20, 30, 50};
    if (bald > sizeof(bald_table)) return 0;
    return bald_table[bald];
}

/**
 * @brief 均衡开启电压寄存器值转换为实际电压
 * @param balv 均衡开启电压寄存器值
 * @return 实际电压值，单位：mV
 */
unsigned short sh367601b_converter_balv_to_voltage(unsigned short balv)
{
    return balv * 20;
}

/**
 * @brief 过放电保护电压寄存器值转换为实际电压
 * @param uv 过放电保护电压寄存器值
 * @return 实际电压值，单位：mV
 */
unsigned short sh367601b_converter_uv_to_voltage(unsigned short uv)
{
    return uv * 10;
}

/**
 * @brief 过放电恢复电压寄存器值转换为实际电压
 * @param uvr 过放电恢复电压寄存器值
 * @return 实际电压值，单位：mV
 */
unsigned short sh367601b_converter_uvr_to_voltage(unsigned short uvr)
{
    return uvr * 20;
}

/**
 * @brief 低压禁止充电电压寄存器值转换为实际电压
 * @param lov 低压禁充电压寄存器值
 * @return 实际电压值，单位：mV，0表示禁用
 */
unsigned short sh367601b_converter_lov_to_voltage(unsigned short lov)
{
    if (!lov) return 0;
    return lov * 250 + 500;
}

/**
 * @brief 放电过流1保护电压寄存器值转换为实际电压
 * @param ocd1v 放电过流1保护电压寄存器值
 * @return 实际电压值，单位：mV，0表示禁用
 */
unsigned short sh367601b_converter_ocd1v_to_voltage(unsigned short ocd1v)
{
    if (!ocd1v) return 0;
    return ocd1v * 525 + 1575;
}

/**
 * @brief 放电过流2保护电压寄存器值转换为实际电压
 * @param ocd2v 放电过流2保护电压寄存器值（0-7）
 * @return 实际电压值，单位：mV
 */
unsigned short sh367601b_converter_ocd2v_to_voltage(unsigned short ocd2v)
{
    const unsigned short ocd2v_table[] = {40, 50, 70, 80, 100, 120, 150, 200};
    if (ocd2v > 7) return 0;
    return ocd2v_table[ocd2v];
}

/**
 * @brief 短路保护电压寄存器值转换为实际电压
 * @param ocd2vd 短路保护电压寄存器值（0-7）
 * @return 实际电压值，单位：mV
 */
unsigned short sh367601b_converter_ocd2vd_to_voltage(unsigned short ocd2vd)
{
    const unsigned short ocd2vd_table[] = {0, 175, 350, 525, 700, 875, 1050, 1225};
    if (ocd2vd > 7) return 0;
    return ocd2vd_table[ocd2vd];
}

/**
 * @brief 充电过流保护电压寄存器值转换为实际电压
 * @param occv 充电过流保护电压寄存器值
 * @return 实际电压值，单位：mV，0表示禁用
 */
unsigned short sh367601b_converter_occv_to_voltage(unsigned short occv)
{
    if (!occv) return 0;
    return (occv * 175) + 350;
}

/**
 * @brief 充电温度保护延时寄存器值转换为实际延时
 * @param tc 充电温度保护延时寄存器值
 * @return 实际延时时间，单位：秒
 */
unsigned char sh367601b_converter_tc_to_delay(char tc)
{
    return tc * 2 + 3;
}
