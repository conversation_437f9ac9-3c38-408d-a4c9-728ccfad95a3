#ifndef _USER_APP_FLASH_H_
#define _USER_APP_FLASH_H_

#include "types.h"

/* Flash存储区域定义 */
#define FLASH_DATA_BASE_ADDR    0x40000
#define FLASH_DATA_SIZE         0x10000
#define USER_APP_FLASH_FLG      0xDA

/* Flash数据类型枚举 */
typedef enum 
{
    FLASH_DATA_BATT_CAPACITY,       /* 电池总容量 */
    FLASH_DATA_RESISTANCE,          /* 采样电阻 */
    FLASH_DATA_SELF_CONSUMPTION,    /* 自耗电 */
    FLASH_DATA_SURPLUS_CAPACITY,    /* 剩余容量 */
    FLASH_DATA_ACC_DISCHARGE,       /* 累计放电 */
    FLASH_DATA_SOC,                 /* soc */
    FLASH_DATA_CHIP_COUNT,          /* 芯片数量配置 */
    FLASH_DATA_CHIP_TYPE,           /* 芯片类型配置 */


    FLASH_FN_DATA_ZERO_CURRENT,        /* 零点电流 */
    FLASH_FN_DATA_VOLTAGE,             /* 单体补偿电压 */
    FLASH_FN_DATA_SERIAL_NUMBER,       /* 产品序列号 */
    FLASH_FN_DATA_EX_FACTORY,          /* 出厂信息 */
    FLASH_DATA_TYPE_MAX
} flash_data_type_e;

/* Flash数据项配置结构体 */
typedef struct 
{
    unsigned int flag_addr;         /* 标志位地址 */
    unsigned int data_addr;         /* 数据地址 */
    unsigned int data_len;          /* 数据长度 */
} flash_data_config_t;

/* Flash数据配置表 */
static const flash_data_config_t flash_data_table[FLASH_DATA_TYPE_MAX] =
{
    /* 标志地址    数据地址    长度    注释 */
    {0x40000, 0x40001, 0x04},   /* FLASH_DATA_BATT_CAPACITY - 电池总容量 */
    {0x4000F, 0x40010, 0x04},   /* FLASH_DATA_RESISTANCE - 采样电阻 */
    {0x4001F, 0x40020, 0x04},   /* FLASH_DATA_SELF_CONSUMPTION - 自耗电 */
    {0x40030, 0x40031, 0x04},   /* FLASH_DATA_SURPLUS_CAPACITY - 剩余容量 */
    {0x40041, 0x40042, 0x04},   /* FLASH_DATA_ACC_DISCHARGE - 累计放电 */
    {0x40052, 0x40053, 0x04},   /* FLASH_DATA_SOC - soc */
    {0x40063, 0x40064, 0x01},   /* FLASH_DATA_CHIP_COUNT - 芯片数量配置 */
    {0x40070, 0x40071, 0x01},   /* FLASH_DATA_CHIP_TYPE - 芯片类型配置 */

    /* FN项目专用数据 */
    {0x40080, 0x40081, 0x04},   /* FLASH_FN_DATA_ZERO_CURRENT - 零点电流 */
    {0x40091, 0x40092, 0x68},   /* FLASH_FN_DATA_VOLTAGE - 单体补偿电压 */
    {0x40105, 0x40106, 0x1E},   /* FLASH_FN_DATA_SERIAL_NUMBER - 产品序列号 */
    {0x40130, 0x40131, 0x08}    /* FLASH_FN_DATA_EX_FACTORY - 出厂信息 */
};

/* Flash操作函数声明 */
extern int My_Flash_Write(unsigned int addr, unsigned char *data, unsigned int len);

/* 高级Flash数据操作函数 */
extern int Flash_Write_Data(flash_data_type_e type, unsigned char *data);
extern int Flash_Read_Data(flash_data_type_e type, unsigned char *data);
extern int Flash_Check_Data_Valid(flash_data_type_e type);
extern void Flash_Clear_Data(flash_data_type_e type);

/* 内联函数：获取数据配置 */
static inline const flash_data_config_t* Flash_Get_Config(flash_data_type_e type)
{
    if (type >= FLASH_DATA_TYPE_MAX) return NULL;
    return &flash_data_table[type];
}

/* 内联函数：获取数据长度 */
static inline unsigned int Flash_Get_Data_Length(flash_data_type_e type)
{
    const flash_data_config_t *config = Flash_Get_Config(type);
    return config ? config->data_len : 0;
}

#endif