#ifndef FN_CUSTOM_DATA_H
#define FN_CUSTOM_DATA_H

#include "types.h"
#include "../../user_app_config.h"
#include "../zy/sh367601xb.h"
/* 前向声明，避免循环引用 */


/* FN项目定制数据配置 */
#define FN_FACTORY_INFO_SIZE        30      /* 出厂信息大小 */
#define FN_PRODUCTION_DATE_SIZE     8       /* 生产日期大小 */
#define FN_VOLTAGE_CALIB_COUNT      16      /* 电压校准通道数量 */

/**
 * @brief FN项目定制数据管理器
 */
typedef struct 
{
    struct 
    {
        /* 出厂信息 30字节 */
        unsigned char factory_info[FN_FACTORY_INFO_SIZE];
        /* 生产日期 8字节 */
        unsigned char production_date[FN_PRODUCTION_DATE_SIZE];
        /* 电压校准 16个short = 32字节 */
        unsigned short voltage_calibration[64];
        /* 零点校准电流 4字节 */
        unsigned int zero_current;
        /* 零点校准电流标志位 1字节 */
        unsigned int zero_current_flg;
    } data;
    
    struct 
    {
        void (*fn_Rs2058_Init)(void);
        void (*fn_Rs2058_Gpio1_Low)(void);
        void (*fn_Rs2058_Gpio1_Hight)(void);
        void (*fn_special_process)(SH367601B_Device *device, Queue* q);
        void (*fn_current_process)(SH367601B_Device *device);
    } method;
} FN_CustomDataManager;

extern void fn_custom_data_init(void);

#endif /* FN_CUSTOM_DATA_H */
