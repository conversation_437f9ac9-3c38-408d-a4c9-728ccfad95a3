/*
 * user_mian.c
 *
 *  Created on: 2024-4-18
 *      Author: lvance
 */
#include "tl_common.h"
#include "drivers.h"
#include "stack/ble/ble.h"
#include "app_main.h"
#include "../att_ble/app_ble.h"
#include "../uart/app_usart.h"
#include "../at_cmd/app_at_cmd.h"

_attribute_data_retention_ tsParaToFlash_t paraToFlash;
_attribute_data_retention_ u8 softwareVer[18]="v1.0.1 ModuleNme\0";
_attribute_data_retention_ u8 blemac[6];
/**
 * @brief      请求链接参数更新
 * @param[in]  none
 * @return
 */
int app_requestNewConnParameterCb(void)
{
	bls_l2cap_requestConnParamUpdate (paraToFlash.conIntervalMin,paraToFlash.conIntervalMax, 0, CONN_TIMEOUT_4S);  // 1 S
    LOG_DEBUG("request new connection parameter min=%d,max=%d,latency=0,timeout=4s\r\n",paraToFlash.conIntervalMin,paraToFlash.conIntervalMax);
	return -1;
}

/**
 * @brief      系统参数出始化函数入口
 * @param[in]  none
 * @return     none
 */
void appSystemParamaterInit(void){

	 LOG_DEBUG("sizeof(tsParaToFlash_t)=%d\r\n",sizeof(tsParaToFlash_t));

     flash_read_data(SYSTEM_PARA_ADDR_BASE,sizeof(tsParaToFlash_t),(unsigned char *)&paraToFlash);
     LOG_DEBUG("power up read from flash data");
     u_array_printf((u8*)&paraToFlash,sizeof(tsParaToFlash_t));
     if (paraToFlash.writeFlag == 0x5CA5)
     {
        /* 读到参数标志  */
        LOG_DEBUG("read from flash successfully\r\n");
     }
     else
     {
        /* 为读到参数标志 */
    	tsParaToFlash_t systemPara = Parameter_init_default;
        paraToFlash = systemPara;
    	flash_read_data(flash_sector_mac_address,sizeof(blemac),(unsigned char *)&blemac);
    	//上电默认广播名称名称，例如YC-B00001
    	memset(&paraToFlash.name,0,sizeof(paraToFlash.name));
    	sprintf((char*)&paraToFlash.name,"YC-BMS%02X%02X%02X",blemac[2],blemac[1],blemac[0]);
    	extern int my_strlen(char* x);
    	paraToFlash.nameLen = my_strlen((char*)&paraToFlash.name);
        LOG_DEBUG("read from flash failed,set to a default parameter\r\n");
        //appSystemParamaterSave();
     }
     memset(softwareVer,0,sizeof(softwareVer));
     sprintf((char *)softwareVer,"v%1d.%1d.%d %s",(u8)(SW_VER>>12)&0x0f,(u8)(SW_VER>>8)&0x0f,(u8)(SW_VER>>0),ModuleNme);
     LOG_DEBUG("software version=%s\r\n",(char *)&softwareVer);
     {
		 //extern gap_periConnectParams_t my_periConnParameters;
		 my_periConnParameters.intervalMin = paraToFlash.conIntervalMin;
		 my_periConnParameters.intervalMax = paraToFlash.conIntervalMax;
		 my_periConnParameters.latency = 0;
		 my_periConnParameters.timeout = 400;
     }

    LOG_DEBUG("writeFlag            =0x%x\r\n",paraToFlash.writeFlag);
    LOG_DEBUG("txPowerIndex         =%d\r\n",paraToFlash.txPowerIndex);
    LOG_DEBUG("brate                =%d\r\n",paraToFlash.brate);
    LOG_DEBUG("advIntervalMin       =%d(unit 0.625MS)\r\n",paraToFlash.advIntervalMin);
    LOG_DEBUG("advIntervalMax       =%d(unit 0.625MS)\r\n",paraToFlash.advIntervalMax);
    LOG_DEBUG("conIntervalMin       =%d(unit 1.25MS)\r\n",paraToFlash.conIntervalMin);
    LOG_DEBUG("conIntervalMax       =%d(unit 1.25MS)\r\n",paraToFlash.conIntervalMax);
    LOG_DEBUG("nameLen              =%d\r\n",paraToFlash.nameLen);
    LOG_DEBUG("name              	=%s\r\n",paraToFlash.name);
    if(paraToFlash.advLen){
    	tlkapi_send_string_data(APP_LOG_EN,"[USER][LOG] ADV DATA",paraToFlash.advData,paraToFlash.advLen);
    }
    LOG_DEBUG("auth                 =%d,%06d\r\n",paraToFlash.authEnable,paraToFlash.authWord);

     
}


/**
 * @brief      系统参数存储
 * @param[in]  none
 * @return     none
 */
void appSystemParamaterSave(void){

    tsParaToFlash_t systemPara;
    u8 saveFlag=0;
    flash_read_data(SYSTEM_PARA_ADDR_BASE,sizeof(tsParaToFlash_t),(unsigned char *)&systemPara);
    LOG_DEBUG("read from flash data");
    u_array_printf((u8*)&systemPara,sizeof(tsParaToFlash_t));
    u8* s1= (u8 *)&systemPara;
    u8* s2=(u8 *)&paraToFlash;
    for(u8 i=0;i<sizeof(paraToFlash);i++){
    	if(*s1++ != *s2++){
    		saveFlag=1;
    		break;
    	}
    }


    if(saveFlag){
    	flash_erase_sector(SYSTEM_PARA_ADDR_BASE);
    	paraToFlash.writeFlag = write_flag;
    	flash_write_page(SYSTEM_PARA_ADDR_BASE,sizeof(tsParaToFlash_t),(unsigned char *)&paraToFlash);
    	LOG_DEBUG("flash save          ");
        u_array_printf((u8*)&paraToFlash,sizeof(paraToFlash));
    }else{
    	tlkapi_send_string_data(APP_LOG_EN,"[APP][USER] flash save unnecessary",0,0);
    }

}

/**
 * @brief      用户获取休眠状态标志
 * @param[in]  none
 * @return     1：不可进入休眠；0：可以进入休眠
 */
u8 appGetBusyStatus(void){

    //连接不休眠
    if(app_get_uart_busy()|| (blc_ll_getCurrentState() == BLS_LINK_STATE_CONN) || appBleWakeupStatusGet()){
    	return 1;
    }

    return 0;

}

/**
 * @brief      用户初始化函数入口
 * @param[in]  setupMode - 启动方式，0-深度休眠中启动；1-deepSleepRetention 启动
 * @return     none
 */
void appInit(unsigned int setupMode){

    if (setupMode)
    {
        /* code */
    }else{

    }

    //LOG_DEBUG("ble setup mode =%d\r\n",setupMode);
    app_usart_int(paraToFlash.brate, setupMode);
    // appBleWakeupStatusSet(1);//高电平唤醒
    //appBleConnStatusInit(setupMode);
}



/**
 * @brief      用户任务函数集合入口
 * @param[in]  none 
 * @return     none
 */
void appMainProc(void){
	app_handle_mcu_wakeup_staus();
    // appAtCmdRemainProc();
}
