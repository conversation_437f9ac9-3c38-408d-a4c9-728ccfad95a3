#ifndef SH367601XB_PARSER_H
#define SH367601XB_PARSER_H

#include "sh367601xb.h"

/* ==========================================数据解析模块声明========================================== */

/**
 * @brief 解析ROM寄存器数据
 */
extern void sh367601b_parser_parse_rom(User_App_Sh3607601x_Rom_TypeDef* rom, unsigned char *data);

/**
 * @brief 解析RAM寄存器数据
 */
extern void sh367601b_parser_parse_ram(User_App_Sh3607601x_Ram_TypeDef* ram, unsigned char *data);

/**
 * @brief 打印ROM配置信息
 */
extern void sh367601b_parser_print_rom(SH367601B_Device* self);

/**
 * @brief 打印RAM传感器数据
 */
extern void sh367601b_parser_print_ram(SH367601B_Device* self);

#endif /* SH367601XB_PARSER_H */
