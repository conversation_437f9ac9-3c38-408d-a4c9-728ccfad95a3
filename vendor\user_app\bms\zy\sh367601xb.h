#ifndef SH367601XB_H_
#define SH367601XB_H_

/**
 * @file sh367601xb.h
 * @brief ********* BMS芯片驱动库 - 继承BMS数据管理系统
 * @version 1.0
 * @date 2025
 *
 * 本文件提供了********* BMS芯片的面向对象封装，继承BMS数据管理系统：
 * - 设备配置管理
 * - 通信驱动接口
 * - 数据解析处理
 * - 数据转换功能
 * - BMS数据管理集成
 */

/* 继承BMS数据管理系统 */
#include "../bms_data.h"
#include "../../list/list_type.h"

/* 宏定义 */
#define ROM_ADDR_START 0x00                 /* rom寄存器初始地址 */
#define ROM_ADDR_LEN   0x15                 /* rom寄存器总长度 */
#define RAM_ADDR_START 0x40                 /* ram寄存器初始地址 */
#define RAM_ADDR_LEN   0x2E                 /* ram寄存器总长度 */
#define RAM_CURRENT_ADDR_START 0x4D         /* ram寄存器电流数据地址 */
#define RAM_CURRENT_ADDR_LEN   0x02         /* ram寄存器电流数据 */

/* 芯片类型定义 */
typedef enum {
    *********_CHIP_10_SERIES = 0,    /* 10串芯片，最大支持10串电池 */
    *********_CHIP_14_SERIES = 1,    /* 14串芯片，最大支持14串电池 */
    *********_CHIP_16_SERIES = 2     /* 16串芯片，最大支持16串电池 */
} *********_ChipType;

/* 芯片类型对应的最大串数 */
#define MAX_CELLS_10_SERIES 10
#define MAX_CELLS_14_SERIES 14
#define MAX_CELLS_16_SERIES 16

/* 多芯片支持配置 */
#define MAX_CHIP_COUNT 4                               /* 最大芯片数量，可配置 */


/* ROM */
typedef struct 
{
    /* 0CH */
    unsigned char occv:5;       /* 充电过流保护电压设置位 */
    unsigned char occt:2;       /* 充电过流保护延时设置位 */
    /* 00H */
    unsigned char id;           /* ID */
    /* 01H */
    unsigned char enmosr:1;     /* 放电mosfet强制开启恢复延时控制位 */
    unsigned char chys:1;       /* 退出过充电保护及充电高低温保护功能变化控制位 */
    unsigned char tc:2;         /* 充电状态下，充电温度保护延时设置位（配置chys为1时才生效） */
    unsigned char cn:4;         /* 电池串数配置控制位 */
    /* 02H */
    unsigned char bals:1;       /* 均衡开启条件控制位 */
    unsigned char chs:1;        /* 充电状态检测电压设置位 */
    unsigned char ocra:1;       /* 放电过流保护自恢复使能位 */
    unsigned char eovr:1;       /* 过充电保护恢复条件设置位 */ 
    unsigned char euvr:1;       /* 过放电保护恢复条件设置位 */
    unsigned char eow:1;        /* 断线保护使能位 */
    unsigned char eot3:1;       /* TS3温度检测点功能设置位 */
    unsigned char enmos:1;      /* 充放电mosfet强制开启使能位 */
    /* 03H,04H,05H */
    unsigned char ovt:2;        /* 过充电保护延时设置位 */
    unsigned short ov:10;       /* 过充电保护电压 */
    unsigned short ovr:9;       /* 过充电恢复电压 */
    /* 06H */
    unsigned char uvr;          /* 过放电恢复电压 */
    /* 07H,08H */
    unsigned char lov:3;        /* 低电压禁止充电电压设置位 */
    unsigned char balt:1;       /* 均衡进入延时设置位 */
    unsigned char uvt:2;        /* 过放电保护延时设置位 */
    unsigned short uv:9;        /* 过放电保护电压 */   
    /* 09H */
    unsigned char balv;         /* 均衡开启电压 */
    /* 0AH */
    unsigned char bald:2;       /* 均衡开启压差设置位 */
    unsigned char ocd1v:4;      /* 放电过流1保护电压设置位 */
    unsigned char ocd1t:2;      /* 放电过流1保护延时设置位 */
    /* 0BH */
    unsigned char sct:2;        /* 短路保护延时设置位 */
    unsigned char ocd2v:3;      /* 放电过流2保护电压设置位 */
    unsigned char ocd2t:2;      /* 放电过流2保护延时设置位 */
    /* 0DH */
    unsigned char otc;          /* 充电高温保护阈值 */
    /* 0EH */
    unsigned char otcr;         /* 充电高温保护释放阈值 */
    /* 0FH */
    unsigned char otd;          /* 放电高温保护阈值 */
    /* 10H */
    unsigned char otdr;         /* 放电高温保护释放阈值 */
    /* 11H */
    unsigned char utc;          /* 充电低温保护阈值 */
    /* 12H */
    unsigned char utcr;         /* 充电低温保护释放阈值 */
    /* 13H */
    unsigned char utd;          /* 放电低温保护阈值 */
    /* 14H */
    unsigned char utdr;         /* 放电低温保护释放阈值 */
}User_App_Sh3607601x_Rom_TypeDef;
/* RAM */
typedef struct
{
    /* 40H */
    unsigned char l0v:1;        /* 低电压禁止充电状态位 */
    unsigned char ow:1;         /* 断线保护状态位 */
    unsigned char sc:1;         /* 短路保护状态位 */
    unsigned char occ:1;        /* 充电过流保护状态位 */
    unsigned char ocd1:1;       /* 放电过流1保护状态位 */
    unsigned char ocd2:1;       /* 放电过流2保护状态位 */
    unsigned char uv:1;         /* 欠压保护状态位 */
    unsigned char ov:1;         /* 过压保护状态位 */
    /* 41H */
    unsigned char oti:1;        /* 内部高温保护状态位 */
    unsigned char otd:1;        /* 放电高温保护状态位 */
    unsigned char utd:1;        /* 放电低温保护状态位 */
    unsigned char otc:1;        /* 充电高温保护状态位 */
    unsigned char utc:1;        /* 充电低温保护状态位 */
    /* 42H */
    unsigned char bal:1;        /* 均衡状态位 */
    unsigned char pd:1;         /* 下电模式状态位 */
    unsigned char ctld:1;       /* ctld状态位 */
    unsigned char pro:1;        /* 烧写EEPORM状态位 */
    unsigned char chging:1;     /* 充电状态位 */
    unsigned char dsging:1;     /* 放电状态位 */
    unsigned char chg_fet:1;    /* 充电mosfet开关状态位 */
    unsigned char dsg_fet:1;    /* 放电mosfet开关状态位 */
    /* 43H */
    unsigned char l0v_flg:1;    /* 低电压禁止充电标志位 */
    unsigned char ow_flg:1;     /* 断线保护标志位 */
    unsigned char sc_flg:1;     /* 短路保护标志位 */
    unsigned char occ_flg:1;    /* 充电过流保护标志位 */
    unsigned char ocd1_flg:1;   /* 放电过流1保护标志位 */
    unsigned char ocd2_flg:1;   /* 放电过流2保护标志位 */
    unsigned char uv_flg:1;     /* 欠压保护标志位 */
    unsigned char ov_flg:1;     /* 过压保护标志位 */
    /* 44H */
    unsigned char rst_flg:1;    /* 系统复位标志位 */
    unsigned char oti_flg:1;    /* 内部高温保护标志位 */
    unsigned char otd_flg:1;    /* 放电高温保护标志位 */
    unsigned char utd_flg:1;    /* 放电低温保护标志位 */
    unsigned char otc_flg:1;    /* 充电高温保护标志位 */
    unsigned char utc_flg:1;    /* 充电低温保护标志位 */
    /* 45H,46H */
    unsigned short temp1;       /* 温度寄存器1 */
    /* 47H,48H */
    unsigned short temp2;       /* 温度寄存器2 */
    /* 49H,4AH */
    unsigned short temp3;       /* 温度寄存器3 */
    /* 4BH,4CH */
    unsigned short tempn;       /* 芯片内部温度寄存器 */
    /* 4DH,4EH */
    unsigned short cur;         /* 电流寄存器 */
    /* 4FH,50H */
    unsigned short cell1;       /* 电芯电压寄存器1 */
    /* 51H,52H */
    unsigned short cell2;       /* 电芯电压寄存器2 */
    /* 53H,54H */
    unsigned short cell3;       /* 电芯电压寄存器3 */
    /* 55H,56H */
    unsigned short cell4;       /* 电芯电压寄存器4 */ 
    /* 57H,58H */
    unsigned short cell5;       /* 电芯电压寄存器5 */ 
    /* 59H,5AH */
    unsigned short cell6;       /* 电芯电压寄存器6 */ 
    /* 5BH,5CH */
    unsigned short cell7;       /* 电芯电压寄存器7 */ 
    /* 5DH,5EH */
    unsigned short cell8;       /* 电芯电压寄存器8 */  
    /* 5FH,60H */
    unsigned short cell9;       /* 电芯电压寄存器9 */  
    /* 61H,62H */
    unsigned short cell10;      /* 电芯电压寄存器10 */  
    /* 63H,64H */
    unsigned short cell11;      /* 电芯电压寄存器11 */   
    /* 65H,66H */
    unsigned short cell12;      /* 电芯电压寄存器12 */   
    /* 67H,68H */
    unsigned short cell13;      /* 电芯电压寄存器13 */   
    /* 69H,6AH */
    unsigned short cell14;      /* 电芯电压寄存器14 */
    /* 6BH,6CH */
    unsigned short cell15;      /* 电芯电压寄存器15 */  
    /* 6DH,6EH */
    unsigned short cell16;      /* 电芯电压寄存器16 */
}User_App_Sh3607601x_Ram_TypeDef;

/* ==========================================面向对象封装========================================== */

/* 前向声明 */
typedef struct *********_Device *********_Device;

/**
 * @brief *********设备类 - 配置管理模块
 */
typedef struct 
{
    struct 
    {
        /* 配置数据暂时为空，所有配置直接通过方法操作 */
    } data;
    
    struct 
    {
        void (*set_id)(*********_Device* self, unsigned char id);
        void (*set_enmosr)(*********_Device* self, unsigned char enmosr);
        void (*set_chys)(*********_Device* self, unsigned char chys);
        void (*set_tc)(*********_Device* self, unsigned char tc);
        void (*set_cn)(*********_Device* self, unsigned char chip_index, unsigned char cn);
        void (*set_bals)(*********_Device* self, unsigned char bals);
        void (*set_chs)(*********_Device* self, unsigned char chs);
        void (*set_ocra)(*********_Device* self, unsigned char ocra);
        void (*set_eovr)(*********_Device* self, unsigned char eovr);
        void (*set_euvr)(*********_Device* self, unsigned char euvr);
        void (*set_eow)(*********_Device* self, unsigned char eow);
        void (*set_eot3)(*********_Device* self, unsigned char eot3);
        void (*set_enmos)(*********_Device* self, unsigned char enmos);
        void (*set_ovt)(*********_Device* self, unsigned short ovt);
        void (*set_ov)(*********_Device* self, unsigned short ov);
        void (*set_ovr)(*********_Device* self, unsigned short ovr);
        void (*set_uvr)(*********_Device* self, unsigned short uvr);
        void (*set_lov)(*********_Device* self, short lov);
        void (*set_balt)(*********_Device* self, unsigned char balt);
        void (*set_uvt)(*********_Device* self, unsigned short uvt);
        void (*set_uv)(*********_Device* self, unsigned short uv);
        void (*set_balv)(*********_Device* self, unsigned short balv);
        void (*set_bald)(*********_Device* self, unsigned char bald);
        void (*set_ocd1v)(*********_Device* self, unsigned short ocd1v);
        void (*set_ocd1t)(*********_Device* self, unsigned short ocd1t);
        void (*set_sct)(*********_Device* self, unsigned char sct);
        void (*set_ocd2v)(*********_Device* self, unsigned char ocd2v);
        void (*set_ocd2t)(*********_Device* self, short ocd2t);
        void (*set_occv)(*********_Device* self, short occv);
        void (*set_occt)(*********_Device* self, unsigned short occt);
        void (*set_otc)(*********_Device* self, unsigned char otc);
        void (*set_otcr)(*********_Device* self, unsigned char otcr);
        void (*set_otd)(*********_Device* self, unsigned char otd);
        void (*set_otdr)(*********_Device* self, unsigned char otdr);
        void (*set_utc)(*********_Device* self, unsigned char utc);
        void (*set_utcr)(*********_Device* self, unsigned char utcr);
        void (*set_utd)(*********_Device* self, unsigned char utd);
        void (*set_utdr)(*********_Device* self, unsigned char utdr);

        /* ROM数据打包方法 */
        void (*pack_rom_data)(unsigned char *buffer, User_App_Sh3607601x_Rom_TypeDef *rom);
        /* ROM副本管理方法 */
        void (*create_rom_copy)(*********_Device* self);
    } method;
} *********_Config;

/**
 * @brief *********设备类 - 通信驱动模块
 */
typedef struct 
{
    struct 
    {
        /* 通信数据暂时为空，所有通信直接通过方法操作 */
    } data;
    
    struct 
    {
        void (*reset)(void);
        void (*write_command)(void);
        void (*write_data)(unsigned char data, unsigned char addr);
        void (*read_rom)(unsigned char addr, unsigned char recv_len);
        void (*read_ram)(unsigned char addr, unsigned char recv_len);
    } method;
} *********_Communication;

/**
 * @brief *********设备类 - 数据解析模块
 */
typedef struct 
{
    struct 
    {
        /* 解析数据暂时为空，所有解析直接通过方法操作 */
    } data;
    
    struct 
    {
        void (*parse_rom)(User_App_Sh3607601x_Rom_TypeDef* rom, unsigned char *data);
        void (*parse_ram)(User_App_Sh3607601x_Ram_TypeDef* ram, unsigned char *data);
        void (*print_rom)(*********_Device* self);
        void (*print_ram)(*********_Device* self);
        void (*pack_rom_data)(unsigned char* buffer, User_App_Sh3607601x_Rom_TypeDef* rom_data);
        void (*create_rom_copy)(*********_Device* self);
    } method;
} *********_Parser;

/**
 * @brief *********设备类 - 工具函数模块
 */
typedef struct 
{
    struct 
    {
        /* 工具数据暂时为空，所有工具操作直接通过方法操作 */
    } data;
    
    struct 
    {
        /* NTC温度转换函数 */
        char (*calc_temp_from_resistance)(float resistance_ohm, char temp_offset_celsius);
        char (*calc_temp_from_adc)(unsigned short adc_reg_value);
        char (*calc_low_temp_from_reg)(unsigned char reg_value);
        char (*calc_external_temp)(unsigned short temp_reg);
        float (*find_resistance_from_temp)(char temp_celsius, char temp_offset_celsius);
        unsigned char (*calc_high_temp_reg)(char temp_celsius);
        unsigned char (*calc_low_temp_reg)(char temp_celsius);
        
        /* 电流转换函数 */
        int (*calc_current_from_adc)(unsigned short adc_reg_value, float sampling_resistance_mohm);
        
        /* 电压转换函数 */
        unsigned short (*reg_from_voltage)(unsigned short cell);
    } method;
} *********_Tool;


/**
 * @brief *********设备类 - 数据转换模块
 */
typedef struct 
{
    struct 
    {
        /* 转换数据暂时为空，所有转换直接通过方法操作 */
    } data;
    
    struct 
    {
        unsigned short (*ov_to_voltage)(unsigned short ov);
        unsigned short (*ovr_to_voltage)(unsigned short ovr);
        unsigned short (*balv_to_voltage)(unsigned short balv);
        unsigned short (*uv_to_voltage)(unsigned short uv);
        unsigned short (*uvr_to_voltage)(unsigned short uvr);
        unsigned short (*lov_to_voltage)(unsigned short lov);
        unsigned short (*ocd1v_to_voltage)(unsigned short ocd1v);
        unsigned short (*ocd2v_to_voltage)(unsigned short ocd2v);
        unsigned short (*ocd2vd_to_voltage)(unsigned short ocd2vd);
        unsigned short (*occv_to_voltage)(unsigned short occv);
        unsigned char (*tc_to_delay)(char tc);

        /* 延时转换函数指针 */
        unsigned short (*ovt_to_value)(unsigned char ovt);
        unsigned short (*uvt_to_value)(unsigned char uvt);
        unsigned short (*ocd1t_to_value)(unsigned char ocd1t);
        unsigned short (*ocd2t_to_value)(unsigned char ocd2t);
        unsigned short (*occt_to_value)(unsigned char occt);
        unsigned short (*balt_to_value)(unsigned char balt);
        unsigned short (*bald_to_value)(unsigned char bald);
    } method;
} *********_Converter;

/**
 * @brief *********设备类 - 芯片管理模块
 */
typedef struct 
{
    struct 
    {
        *********_ChipType chip_type[MAX_CHIP_COUNT];  /* 芯片类型数组 */
        unsigned char chip_count;                      /* 当前使用的芯片数量 */
        unsigned char current_chip_index;              /* 当前选中的芯片索引 */
        unsigned char write_rom_in_progress;           /* 写ROM流程进行中标志 */
        unsigned char write_rom_target_chip_count;     /* 写ROM目标芯片数量 */
        /* 芯片硬件切换函数指针数组 */
        void (*chip_switch_functions[MAX_CHIP_COUNT])(void);  /* 每个芯片的硬件切换实现函数 */
    } data;
    
    struct
    {
        /* 切换到指定芯片 */
        void (*switch_to_chip)(*********_Device* device, unsigned char chip_index);
        /* 写入ROM数据（基于队列机制） */
        void (*write_rom_for_chip)(*********_Device* device, Queue* q);
        /* 设置芯片数量 */
        void (*set_chip_count)(*********_Device* device, unsigned char chip_count);
        /* 启动批量写ROM流程（自动遍历所有芯片） */
        void (*start_batch_write_rom)(*********_Device* device, Queue* q);
        /* 设置单个芯片类型 */
        void (*set_chip_type)(*********_Device* device, unsigned char chip_index, *********_ChipType chip_type);
        /* 获取单个芯片类型 */
        *********_ChipType (*get_chip_type)(*********_Device* device, unsigned char chip_index);
        /* 获取单个芯片最大串数 */
        unsigned char (*get_chip_max_cells)(*********_Device* device, unsigned char chip_index);
        /* 处理复位任务中的芯片切换逻辑 */
        int (*handle_reset_task_chip_switching)(*********_Device* device, Queue* q);
        /* 处理RAM读取任务中的芯片切换逻辑 */
        int (*handle_ram_read_chip_switching)(*********_Device* device, Queue* q, char is_rom_task);
        /* 设置单个芯片的硬件切换函数 */
        void (*set_chip_switch_function)(*********_Device* device, unsigned char chip_index, void (*switch_func)(void));
    } method;
} *********_Chip;

/**
 * @brief *********设备主类 - 继承BMS数据管理系统
 */
struct *********_Device {
    /* 内嵌BMS数据管理系统*/
    BMS_DataManager bms_system;                    /* BMS数据管理系统实例 */

    /* *********芯片特有数据成员 */
    User_App_Sh3607601x_Rom_TypeDef rom[MAX_CHIP_COUNT];           /* ROM寄存器数据数组 */
    User_App_Sh3607601x_Ram_TypeDef ram[MAX_CHIP_COUNT];           /* RAM寄存器数据数组 */
    User_App_Sh3607601x_Rom_TypeDef write_rom;     /* 每个芯片的写ROM副本数据数组 */
    unsigned char write_buff[MAX_CHIP_COUNT][ROM_ADDR_LEN];        /* 每个芯片的写入数据缓冲区 */
    char write_flags[ROM_ADDR_LEN];                /* 每个芯片的写入标志数组 */

    /* 设备状态 */
    bool is_initialized;                           /* 设备是否已初始化 */
    bool is_connected;                             /* 设备是否已连接 */

    /* *********功能模块 */
    *********_Config config;                       /* 配置管理模块 */
    *********_Communication comm;                  /* 通信驱动模块 */
    *********_Parser parser;                       /* 数据解析模块 */
    *********_Converter converter;                 /* 数据转换模块 */
    *********_Tool tool;                           /* 工具函数模块 */
    *********_Chip chip;                           /* 芯片管理模块 */

    /* BMS数据同步方法 */
    struct 
    {
        /* 实时数据更新方法 */
        void (*update_realtime_data)(*********_Device* self);
        /* 保护配置更新方法 */
        void (*update_protection_config)(*********_Device* self);
    } bms_sync;
};
typedef struct *********_Device SH367601XB_Device;

/* ==========================================类方法声明========================================== */

/**
 * @brief 初始化*********设备实例（自动初始化内嵌BMS系统）
 * @param device 设备实例指针
 * @return 0=成功，-1=失败
 */
extern int sh367601b_init(*********_Device* device);


#endif
