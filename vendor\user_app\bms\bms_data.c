#include "tl_common.h"
#include "bms_data.h"
#include "bms_data_tool.h"

/* ==========================================通用管理器初始化宏========================================== */

/**
 * @brief 定义带专业数据处理方法的管理器初始化宏
 * @param ManagerType 管理器类型名
 * @param ProcessMethod 专业数据处理方法名
 */
#define DEFINE_MANAGER_WITH_PROCESSOR(ManagerType, ProcessMethod) \
static void manager_init_##ManagerType(ManagerType* self) { \
    if (self == NULL) return; \
    memset(&self->data, 0, sizeof(self->data)); \
    self->methods.ProcessMethod = ManagerType##_##ProcessMethod; \
} \
int manager_init_func_##ManagerType(ManagerType* self) { \
    if (self == NULL) return -1; \
    self->methods.init = manager_init_##ManagerType; \
    /* 确保专业数据处理方法指针在这里也设置 */ \
    self->methods.ProcessMethod = ManagerType##_##ProcessMethod; \
    return 0; \
}



/* ==========================================各管理器数据处理方法实现========================================== */
/* 电压管理器专业数据处理方法 */
static void VoltageManager_process_voltage_data(VoltageManager* self, unsigned short* cells, int battery_count)
{
    if (self == NULL || cells == NULL) return;

    unsigned long total = 0;
    unsigned short max_voltage = 0, min_voltage = 65535;

    self->data.battery_count = battery_count;

    for (int i = 0; i < battery_count; i++)
    {
        self->data.cell_voltages[i] = cells[i];
        if (self->data.cell_voltages[i] == 0 || self->data.cell_voltages[i] > 5000) 
            self->data.cell_voltages[i] = 0;

        /* 计算统计值 */
        total += self->data.cell_voltages[i];
        if (self->data.cell_voltages[i] > max_voltage) 
            max_voltage = self->data.cell_voltages[i];
        if (self->data.cell_voltages[i] < min_voltage) 
            min_voltage = self->data.cell_voltages[i];
    }

    self->data.total_voltage = (unsigned int)total;
    self->data.max_voltage = max_voltage;
    self->data.min_voltage = min_voltage;
    /* 计算平均电压 */
    self->data.average_voltage = self->data.battery_count > 0 ?
                                (unsigned short)(total / self->data.battery_count) : 0;
    self->data.voltage_diff = max_voltage - min_voltage;

    printf("VoltageManager: Processed (Cells: %d, Total: %d mV, Max: %d mV, Min: %d mV, Avg: %d mV, Diff: %d mV)\n",
           self->data.battery_count, self->data.total_voltage, self->data.max_voltage,
           self->data.min_voltage, self->data.average_voltage, self->data.voltage_diff);
}
/* 电流管理器专业数据处理方法 */
static void CurrentManager_process_current_data(CurrentManager* self, unsigned int current, char direction)
{
    if (self == NULL) return;

    /* 更新电流管理器数据 */
    self->data.total_current = hybridFilter((float)current);

    /* 更新电流状态 */
    self->data.current_state = direction;
    printf("CurrentManager:(Current: %d mA, State: %d)\n",current, self->data.current_state);
}
/* 温度管理器专业数据处理方法 */
static void TemperatureManager_process_temperature_data(TemperatureManager* self, signed char* external_temps, unsigned char temp_count, signed char chip_temp)
{
    if (self == NULL || external_temps == NULL) return;

    /* 更新温度传感器数量 */
    self->data.external_temp_count = temp_count > 10 ? 10 : temp_count;
    self->data.chip_temp_count = 1;
    self->data.mos_temp_count = 0; // 暂时未使用

    /* 更新芯片温度 */
    self->data.chip_temp = chip_temp;

    /* 更新外部温度数组 */
    signed char max_temp = external_temps[0], min_temp = external_temps[0];
    for (int i = 0; i < self->data.external_temp_count; i++) {
        self->data.external_temp[i] = external_temps[i];
        if (external_temps[i] > max_temp) max_temp = external_temps[i];
        if (external_temps[i] < min_temp) min_temp = external_temps[i];
    }
    
    /* 计算温度统计值 */
    self->data.max_external_temp = max_temp;
    self->data.min_external_temp = min_temp;

    printf("TemperatureManager: Processed (%d sensors, Range: %d-%d Chip: %d)\n",
           self->data.external_temp_count, min_temp, max_temp, chip_temp);
}
/* 电池状态管理器专业数据处理方法 */
static void BatteryStateManager_process_battery_state_data(BatteryStateManager* self, unsigned int cycle_count, float current_ma, float battery_capacity, char direction)
{
    if (self == NULL) return;

    /* 1. 更新采样时间 */
    self->data.sampling_time_curr = clock_time();
    unsigned int time_ms = 0;
    if (self->data.sampling_time_last > 0 && self->data.sampling_time_curr > self->data.sampling_time_last) {
        time_ms = (self->data.sampling_time_curr - self->data.sampling_time_last) / 16000;
    }
    self->data.sampling_time_last = self->data.sampling_time_curr;

    /* 2. 计算SOC */
    if (time_ms > 0 && battery_capacity > 0) 
        /* 调用SOC更新函数 */
        self->data.soc = soc_compute(self->data.soc, battery_capacity, current_ma, (float)time_ms, direction);

    /* 3. 计算SOH（电池健康度） - 基于循环次数 */
    if (cycle_count > 0) 
    {
        /* 每1000个循环容量衰减30%，最大衰减30% */
        float degradation_rate = (float)cycle_count / 1000.0f * 0.3f;
        if (degradation_rate > 0.3f) degradation_rate = 0.3f;
        self->data.soh = (unsigned short)((1.0f - degradation_rate) * 10000.0f);  // 0.01%精度
    } 
    else 
        self->data.soh = 10000; // 新电池健康度为100.00%
    
    /* SOH范围限制 */
    if (self->data.soh > 10000) self->data.soh = 10000;  // 100.00%
    if (self->data.soh < 7000) self->data.soh = 7000;    // 70.00%

    printf("BatteryStateManager: Processed (SOC: %d%%, SOH: %d%%, CurrTime: %d ms)\n",
           (int)(self->data.soc * 10000), (int)(self->data.soh / 100),
           time_ms);
}
/* 充放电管理器专业数据处理方法 */
static void ChargeDischargeManager_process_charge_discharge_data(ChargeDischargeManager* self, int current_ma, char direction, unsigned int total_voltage_mv, unsigned int battery_capacity)
{
    if (self == NULL) return;
    
    int filtered_current = current_ma;

    /* 1. 计算时间间隔 */
    static unsigned long last_update_time = 0;
    unsigned long current_time = clock_time();
    int time_delta_seconds = 0.0f;
    
    if (last_update_time > 0 && current_time > last_update_time) 
        time_delta_seconds = ((current_time - last_update_time) / 16000); // ms转秒
    last_update_time = current_time;
    
    /* 2. 累计容量计算 */
    if (time_delta_seconds > 0) 
    {
        float capacity_delta_mah = (filtered_current * time_delta_seconds) / 3600.0f; // mAh
        
        if (CURRENT_STATE_DISCHARGING == direction && capacity_delta_mah > 0) {
            /* 放电状态：累计放电量 */
            self->data.accumulated_discharge += (unsigned int)capacity_delta_mah;
            
            /* 更新剩余容量 */
            if (self->data.remaining_capacity > capacity_delta_mah) 
                self->data.remaining_capacity -= (unsigned int)capacity_delta_mah;
            else 
                self->data.remaining_capacity = 0;
        } 
        else if (CURRENT_STATE_CHARGING == direction && capacity_delta_mah < 0) 
        {
            /* 充电状态：累计充电量 */
            self->data.accumulated_charge += (unsigned int)(-capacity_delta_mah);
            /* 更新剩余容量 */
            self->data.remaining_capacity += (unsigned int)(-capacity_delta_mah);
        }
    }

    /* 3. 更新功率 */
    // printf("filtered_current: %d, total_voltage_mv: %d\n", filtered_current, total_voltage_mv);
    self->data.power = filtered_current * total_voltage_mv; // 转换为mW

    /* 4. 更新循环计数 - 基于累计放电量计算 */
    if (self->data.accumulated_discharge > 0 && battery_capacity > 0) {
        unsigned int new_cycle_count = self->data.accumulated_discharge / battery_capacity;
        
        /* 检测循环数增加 */
        if (new_cycle_count > self->data.cycle_count)
            self->data.cycle_count = new_cycle_count;
    }

    printf("ChargeDischargeManager: Processed (Power: %d W, Cycle: %d, RemainCap: %d mAh, RemainTime: %d min, AccCharge: %d mAh, AccDischarge: %d mAh)\n",
           (int)self->data.power / 1000, self->data.cycle_count, self->data.remaining_capacity,
           self->data.remaining_time, self->data.accumulated_charge, self->data.accumulated_discharge);
}
/* 告警管理器专业数据处理方法 */
static void AlarmManager_process_alarm_data(AlarmManager* self, bool charge_over_temp, bool charge_under_temp,
                                          bool discharge_over_temp, bool discharge_under_temp, bool charge_overcurrent,
                                          bool discharge_overcurrent1, bool discharge_overcurrent2, bool undervoltage, bool overvoltage)
{
    if (self == NULL) return;

    /* 更新告警数据 */
    self->data.charge_over_temp = charge_over_temp;
    self->data.charge_under_temp = charge_under_temp;
    self->data.discharge_over_temp = discharge_over_temp;
    self->data.discharge_under_temp = discharge_under_temp;
    self->data.charge_overcurrent = charge_overcurrent;
    self->data.discharge_overcurrent1 = discharge_overcurrent1;
    self->data.discharge_overcurrent2 = discharge_overcurrent2;
    self->data.undervoltage = undervoltage;
    self->data.overvoltage = overvoltage;

    /* 直接输出各个告警数据 */
    printf("AlarmManager: Processed alarm data (ChgOT: %d, ChgUT: %d, DisOT: %d, DisUT: %d, ChgOC: %d, DisOC1: %d, DisOC2: %d, UV: %d, OV: %d)\n",
           self->data.charge_over_temp, self->data.charge_under_temp,
           self->data.discharge_over_temp, self->data.discharge_under_temp,
           self->data.charge_overcurrent, self->data.discharge_overcurrent1,
           self->data.discharge_overcurrent2, self->data.undervoltage, self->data.overvoltage);
}
/* 状态管理器专业数据处理方法 */
static void StatusManager_process_status_data(StatusManager* self, bool balance_status, bool charge_mos,
                                            bool discharge_mos, bool charge_status, bool discharge_status)
{
    if (self == NULL) return;

    /* 更新状态数据 */
    self->data.balance_status = balance_status;
    self->data.charge_mos = charge_mos;
    self->data.discharge_mos = discharge_mos;
    self->data.charge_status = charge_status;
    self->data.discharge_status = discharge_status;

    /* 直接输出各个状态数据 */
    printf("StatusManager: Processed status data (Balance: %d, ChgMOS: %d, DisMOS: %d, ChgStat: %d, DisStat: %d)\n",
           self->data.balance_status, self->data.charge_mos, self->data.discharge_mos,
           self->data.charge_status, self->data.discharge_status);
}
/* 保护参数管理器专业数据处理方法 */
static void ProtectionParameterManager_process_protection_data(ProtectionParameterManager* self,
                                      unsigned short overvoltage_protection, unsigned short overvoltage_recovery, unsigned short overvoltage_delay,
                                      unsigned short undervoltage_protection, unsigned short undervoltage_recovery, unsigned short undervoltage_delay,
                                      unsigned short discharge_overcurrent1, unsigned short discharge_overcurrent1_delay,
                                      unsigned short discharge_overcurrent2, unsigned short discharge_overcurrent2_delay,
                                      unsigned short charge_overcurrent1, unsigned short charge_overcurrent1_delay,
                                      char charge_high_temp_protection, char charge_high_temp_recovery,
                                      char charge_low_temp_protection, char charge_low_temp_recovery,
                                      char discharge_high_temp_protection, char discharge_high_temp_recovery,
                                      char discharge_low_temp_protection, char discharge_low_temp_recovery,
                                      unsigned short balance_start_voltage_diff, unsigned short balance_start_voltage)
{
    if (self == NULL) return;

    /* 更新保护参数数据 */
    self->data.overvoltage_protection = overvoltage_protection;
    self->data.overvoltage_recovery = overvoltage_recovery;
    self->data.overvoltage_delay = overvoltage_delay;
    self->data.undervoltage_protection = undervoltage_protection;
    self->data.undervoltage_recovery = undervoltage_recovery;
    self->data.undervoltage_delay = undervoltage_delay;

    self->data.discharge_overcurrent1 = discharge_overcurrent1;
    self->data.discharge_overcurrent1_delay = discharge_overcurrent1_delay;
    self->data.discharge_overcurrent2 = discharge_overcurrent2;
    self->data.discharge_overcurrent2_delay = discharge_overcurrent2_delay;
    self->data.charge_overcurrent1 = charge_overcurrent1;
    self->data.charge_overcurrent1_delay = charge_overcurrent1_delay;

    self->data.charge_high_temp_protection = charge_high_temp_protection;
    self->data.charge_high_temp_recovery = charge_high_temp_recovery;
    self->data.charge_low_temp_protection = charge_low_temp_protection;
    self->data.charge_low_temp_recovery = charge_low_temp_recovery;
    self->data.discharge_high_temp_protection = discharge_high_temp_protection;
    self->data.discharge_high_temp_recovery = discharge_high_temp_recovery;
    self->data.discharge_low_temp_protection = discharge_low_temp_protection;
    self->data.discharge_low_temp_recovery = discharge_low_temp_recovery;

    self->data.balance_start_voltage_diff = balance_start_voltage_diff;
    self->data.balance_start_voltage = balance_start_voltage;

    printf("ProtectionParameterManager: Processed (OV: %d/%d/%d, UV: %d/%d/%d, OCD1: %d/%d, OCD2: %d/%d, OCCV: %d/%d, ChgHT: %d/%d, ChgLT: %d/%d, DisHT: %d/%d, DisLT: %d/%d, BalV: %d/%d)\n",
           self->data.overvoltage_protection, self->data.overvoltage_recovery, self->data.overvoltage_delay,
           self->data.undervoltage_protection, self->data.undervoltage_recovery, self->data.undervoltage_delay,
           self->data.discharge_overcurrent1, self->data.discharge_overcurrent1_delay,
           self->data.discharge_overcurrent2, self->data.discharge_overcurrent2_delay,
           self->data.charge_overcurrent1, self->data.charge_overcurrent1_delay,
           self->data.charge_high_temp_protection, self->data.charge_high_temp_recovery,
           self->data.charge_low_temp_protection, self->data.charge_low_temp_recovery,
           self->data.discharge_high_temp_protection, self->data.discharge_high_temp_recovery,
           self->data.discharge_low_temp_protection, self->data.discharge_low_temp_recovery,
           self->data.balance_start_voltage_diff, self->data.balance_start_voltage);
}

/* 自定义参数管理器专业数据处理方法 */
static void CustomParameterManager_process_custom_param_data(CustomParameterManager* self,
                                                           unsigned int battery_total_capacity,
                                                           unsigned int battery_remaining_capacity,
                                                           unsigned short self_consumption,
                                                           unsigned int sampling_resistance)
{
    if (self == NULL) return;

    /* 更新自定义参数数据 */
    self->data.battery_total_capacity = battery_total_capacity;
    self->data.battery_remaining_capacity = battery_remaining_capacity;
    self->data.self_consumption = self_consumption;
    self->data.sampling_resistance = sampling_resistance;
    printf("CustomParameterManager: Processed (TotalCap: %d mAh, RemainCap: %d mAh, SelfConsump: %d mA)\n",
           self->data.battery_total_capacity, self->data.battery_remaining_capacity, self->data.self_consumption);
}



/* ==========================================使用宏定义所有管理器初始化========================================== */
/* 使用宏初始化带专业数据处理方法的管理器 */
DEFINE_MANAGER_WITH_PROCESSOR(AlarmManager, process_alarm_data)
DEFINE_MANAGER_WITH_PROCESSOR(StatusManager, process_status_data)
DEFINE_MANAGER_WITH_PROCESSOR(VoltageManager, process_voltage_data)
DEFINE_MANAGER_WITH_PROCESSOR(CurrentManager, process_current_data)
DEFINE_MANAGER_WITH_PROCESSOR(ChargeDischargeManager, process_charge_discharge_data)
DEFINE_MANAGER_WITH_PROCESSOR(TemperatureManager, process_temperature_data)
DEFINE_MANAGER_WITH_PROCESSOR(BatteryStateManager, process_battery_state_data)
DEFINE_MANAGER_WITH_PROCESSOR(ProtectionParameterManager, process_protection_data)
DEFINE_MANAGER_WITH_PROCESSOR(CustomParameterManager, process_custom_param_data)
/* 专门用于带专业数据处理方法的管理器初始化宏 */
#define INIT_AND_ENABLE_PROCESSOR_MANAGER(self, mgr_name, mgr_type, display_name, process_method) \
    if (manager_init_func_##mgr_type(&self->mgr_name) == 0) { \
        self->mgr_name.methods.init(&self->mgr_name); \
        /* 双重确保专业数据处理方法指针设置正确 */ \
        if (self->mgr_name.methods.process_method == NULL) { \
            self->mgr_name.methods.process_method = mgr_type##_##process_method; \
        } \
    } else { \
    }



/* ==========================================BMS总数据管理器实现========================================== */
/**
 * @brief 初始化BMS数据管理器实例（静态分配版本）
 * @param self BMS数据管理器实例指针
 * @return 0=成功，-1=失败
 */
int bms_init(BMS_DataManager* self)
{
    /* 初始化基本数据成员 */
    self->is_initialized = false;

    /* 直接初始化所有管理器，使用宏初始化带专业数据处理方法的管理器 */
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, alarm_mgr, AlarmManager, "Alarm", process_alarm_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, status_mgr, StatusManager, "Status", process_status_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, voltage_mgr, VoltageManager, "Voltage", process_voltage_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, current_mgr, CurrentManager, "Current", process_current_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, temp_mgr, TemperatureManager, "Temperature", process_temperature_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, charge_discharge_mgr, ChargeDischargeManager, "Charge/Discharge", process_charge_discharge_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, battery_state_mgr, BatteryStateManager, "Battery state", process_battery_state_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, protection_param_mgr, ProtectionParameterManager, "Protection param", process_protection_data);
    INIT_AND_ENABLE_PROCESSOR_MANAGER(self, custom_param_mgr, CustomParameterManager, "Custom param", process_custom_param_data);

    self->is_initialized = true;
    printf("BMS DataManager initialization completed directly (static allocation)\n");
    return 0;
}
