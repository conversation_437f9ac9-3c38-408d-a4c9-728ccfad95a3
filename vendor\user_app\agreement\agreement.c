#include "agreement.h"


/* 根据协议格式选择包含对应的协议头文件 */
#if RZN_PROJECT_ENABLE
#include "rzn/rzn_agreement.h"
#elif FN_PROJECT_ENABLE
#include "fn/fn_agreement.h"
#else
#error "No Bluetooth protocol format enabled! Please enable one protocol in protocol_interface.h"
#endif

/**
 * @brief 统一蓝牙协议数据处理入口
 * @param data 数据指针
 * @param len 数据长度
 * 
 * @note 通过条件编译选择具体的协议格式处理函数
 */
void protocol_process_data(unsigned char *data, unsigned short len)
{
#if RZN_PROJECT_ENABLE
    /* RZN蓝牙协议格式处理 */
    bluetooth_protocol_process(data, len);
    
#elif FN_PROJECT_ENABLE
    /* FN蓝牙协议格式处理 */
    bluetooth_protocol_process(data, len);
#else
    /* 没有启用任何协议格式 */
#endif
} 