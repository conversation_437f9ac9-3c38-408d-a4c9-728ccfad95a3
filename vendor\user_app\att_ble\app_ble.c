/*
 * app_ble.c
 *
 *  Created on: 2024-4-18
 *      Author: lvance
 */

#include "tl_common.h"
#include "drivers.h"
#include "stack/ble/ble.h"
#include "app_ble.h"
#include "../at_cmd/app_at_cmd.h"
#include "../uart/app_usart.h"
#include "../system_main/app_main.h"
#include "../user_app_main.h"
// #include "../user_app_main.h"
/**
 * @brief      write callback of Attribute of TelinkSppDataClient2ServerUUID
 * @param[in]  para - rf_packet_att_write_t
 * @return     0
 */

_attribute_data_retention_ gap_periConnectParams_t my_periConnParameters;
int app_onReceiveData(void *para)
{
	rf_packet_att_write_t *p = (rf_packet_att_write_t*)para;
	u16 len = p->l2capLen - 3;
	if(len > 0)
	{
		// app_usart_fifo_push(&p->value,len);
		// user_app_sh36760x_ble_logic(&p->value,len);
		Ble.len = len;
		memcpy(Ble.buff, &p->value, Ble.len);
		Ble.flg = true;
	}
	tlkapi_send_string_data(APP_LOG_EN,"[USER][LOG] gatt receive = ",&p->value,len);
	return 0;
}

/**
 * @brief      write callback of Attribute of TelinkSppDataClient2ServerUUID
 * @param[in]  para - rf_packet_att_write_t
 * @return     0
 */
int app_onReceiveCmd(void *para)
{
	rf_packet_att_write_t *p = (rf_packet_att_write_t*)para;
	u16 len = p->l2capLen - 3;
	if(len > 0)
	{
		appUartOrGattReceiveHandle(&p->value,len,AT_GATT);
	}

	return 0;
}

u8 appBleConnStatusGet(void){
	if(blc_ll_getCurrentState() == BLS_LINK_STATE_CONN){
		return 1;	
	}else{
		return 0;
	}
}

void appBleConnStatusSet(u8 status){
	if(status){
		gpio_setup_up_down_resistor(BLE_CONN_INDCATE_PIN, PM_PIN_PULLUP_1M);
		gpio_write(BLE_CONN_INDCATE_PIN, 1);
	}else{
		gpio_setup_up_down_resistor(BLE_CONN_INDCATE_PIN, PM_PIN_PULLDOWN_100K);
		gpio_write(BLE_CONN_INDCATE_PIN, 0);		
	}
}

void appBleConnStatusInit(u8 setupMode){
	(void) setupMode;
	gpio_set_func(BLE_CONN_INDCATE_PIN, AS_GPIO);
	gpio_set_input_en(BLE_CONN_INDCATE_PIN, 0);
	gpio_set_output_en(BLE_CONN_INDCATE_PIN, 1);
	gpio_setup_up_down_resistor(BLE_CONN_INDCATE_PIN, PM_PIN_PULLDOWN_100K);
	gpio_write(BLE_CONN_INDCATE_PIN, 0);

	if(blc_ll_getCurrentState() == BLS_LINK_STATE_CONN){
		appBleConnStatusSet(1);
	}else{
		appBleConnStatusSet(0);
	}
}

void appBleWakeupStatusSet(u8 status){

	gpio_set_func(BLE_WAKEUP_PIN, AS_GPIO);
	gpio_set_input_en(BLE_WAKEUP_PIN, 1);
	gpio_set_output_en(BLE_WAKEUP_PIN, 0);
	if(status){
		
		gpio_setup_up_down_resistor(BLE_WAKEUP_PIN,PM_PIN_PULLDOWN_100K );
		gpio_write(BLE_WAKEUP_PIN, 0);
		cpu_set_gpio_wakeup (BLE_WAKEUP_PIN, Level_High,1);  //button pin pad low wakeUp suspend/deepSleep
	}else{
		gpio_setup_up_down_resistor(BLE_WAKEUP_PIN, PM_PIN_PULLUP_1M);
		gpio_write(BLE_WAKEUP_PIN, 1);
		cpu_set_gpio_wakeup (BLE_WAKEUP_PIN, Level_Low,1);  //button pin pad low wakeUp suspend/deepSleep
	}	

}

u8 appBleWakeupStatusGet(void){

	return (gpio_read(BLE_WAKEUP_PIN));
}

#if EXTEND_MTU_LENGTH_ENABLE

unsigned char exchangeLengthFlag=0;
int appExchangeDataLengthCb(void){
    	//BLE_DISCONNECTED;//test
	if(exchangeLengthFlag == FALSE)
	{
		blc_ll_exchangeDataLength(LL_LENGTH_REQ,MAX_DLE);
	}

	return -1;
}


#endif
