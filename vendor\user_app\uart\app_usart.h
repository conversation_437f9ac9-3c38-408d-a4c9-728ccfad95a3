/*
 * app_usart.h
 *
 *  Created on: 2023-7-12
 *      Author: lvance
 */

#ifndef APP_USART_H_
#define APP_USART_H_

#define UART_TX_PIN UART_TX_PD7
// #define UART_RX_PIN UART_RX_PB7
#define UART_RX_PIN UART_RX_PC3


#define BLE_MODULE_INDICATE_DATA_TO_MCU					0

#if BLE_MODULE_INDICATE_DATA_TO_MCU
	#define MCU_WAKEUP_PIN  GPIO_PB6
#endif

#define SPP_RXFIFO_SIZE		256
#define SPP_RXFIFO_NUM		2

#define SPP_TXFIFO_SIZE		256
#define SPP_TXFIFO_NUM		2

#define UART_DATA_LEN    	(SPP_TXFIFO_SIZE - 2)   // data max 252
typedef struct{
    unsigned int len; // data max 252
    unsigned char data[UART_DATA_LEN];
}uart_data_t;


typedef struct 
{
    /* 串口接收缓存 */

    unsigned char done;
    unsigned char len;
    unsigned char data[255];

}tsAppUsartRx_t;

extern tsAppUsartRx_t appUsartRx;
void app_handle_mcu_wakeup_staus(void);
int app_get_uart_busy(void);
void app_usart_irq_pro(void);
int app_usart_fifo_push(unsigned char  *p_buf ,unsigned char len );
void app_usart_int(unsigned int baudRate, unsigned char wakeupMode);
int app_reinit_uartCB(int brate);
// int app_usart_receive_handler (unsigned char *p, int n);
#endif /* APP_USART_H_ */
