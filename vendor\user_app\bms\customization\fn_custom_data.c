#include "fn_custom_data.h"


#if FN_PROJECT_ENABLE
#include "gpio.h"
#include "../../user_app_main.h"

extern FN_CustomDataManager fn_custom_data;
#define USER_APP_ZERO_COUNT 10        /* 零点采样次数 */
#define USER_APP_RS2058_IO1 GPIO_PB6
/* 初始化引脚RS2058 */
static void fn_Rs2058_Init(void)
{
    gpio_set_func(USER_APP_RS2058_IO1, AS_GPIO); 		/* 数字gpio */
	gpio_set_output_en(USER_APP_RS2058_IO1, 1);		/* 使能输出 */
	gpio_set_input_en(USER_APP_RS2058_IO1, 0); 		/* 禁用输入 */
    gpio_write(USER_APP_RS2058_IO1, 0); 
}
/* 拉高 */
static void fn_Rs2058_Gpio1_Hight(void)
{
    gpio_write(USER_APP_RS2058_IO1, 1); 				/* 输出高 */
    printf("io1 hight\n");
    sleep_ms(10);
}
/* 拉低 */
static void fn_Rs2058_Gpio1_Low(void)
{
    gpio_write(USER_APP_RS2058_IO1, 0); 				/* 输出低 */
    printf("io1 low\n");
    sleep_ms(10);
}
/**
 * @brief fn特殊处理函数
 * @param q 队列指针
 * @note 处理零点电流采集、欠压重置、电流状态判断等特殊逻辑
 */
static void fn_special_process(SH367601B_Device *device, Queue* q)
{
    /* 零点电流采集 */
    if (fn_custom_data.data.zero_current_flg)
    {
        static unsigned char zero_current_written = 0;
        int current = device->tool.method.calc_current_from_adc(device->ram[0].cur, 0.000145f);
        fn_custom_data.data.zero_current += current;
        zero_current_written++;
        if (zero_current_written >= USER_APP_ZERO_COUNT)
        {
            fn_custom_data.data.zero_current /= USER_APP_ZERO_COUNT;
            fn_custom_data.data.zero_current_flg = 0;
            zero_current_written = 0;
            /* 只写入Flash一次 */
            Flash_Write_Data(FLASH_FN_DATA_ZERO_CURRENT, (unsigned char *)&fn_custom_data.data.zero_current);
            printf("Zero current calibration completed and saved: %d\n", fn_custom_data.data.zero_current);
        }
    }

    /* 欠压状态下每12秒添加reset任务，累计3分钟后停止 */
    if (device->bms_system.alarm_mgr.data.undervoltage)
    {
        static unsigned int undervoltage_reset_timer = 0;
        static unsigned int undervoltage_start_time = 0;
        static const unsigned int RESET_INTERVAL_MS = 12000;  // 12秒间隔
        static const unsigned int MAX_DURATION_MS = 180000;   // 3分钟总时长

        // 记录欠压开始时间
        if (undervoltage_start_time == 0)
        {
            undervoltage_start_time = clock_time();
            undervoltage_reset_timer = clock_time();
        }

        // 检查是否超过3分钟
        if (!clock_time_exceed(undervoltage_start_time, MAX_DURATION_MS * 1000))
        {
            // 每12秒添加一次reset任务
            if (clock_time_exceed(undervoltage_reset_timer, RESET_INTERVAL_MS * 1000))
            {
                queue_push(q, QUEUE_RESET_TASK, QUEUE_RESET_TIME);
                undervoltage_reset_timer = clock_time();
            }
        }
        else
        {
            // 超过3分钟，停止添加reset任务
            // printf("Undervoltage reset stopped after 3 minutes\n");
        }
    }
    else
    {
        // 欠压状态结束，重置计时器
        static unsigned int undervoltage_start_time = 0;
        undervoltage_start_time = 0;
    }
}
/**
 * @brief fn电流处理函数
 * @note 处理零点电流采集、电流状态判断等特殊逻辑
 */
static void fn_current_process(SH367601B_Device *device)
{
    /* 电流方向和状态判断 */
    if ((device->ram[0].cur >> 15) & 1)
    {
        device->bms_system.current_mgr.data.current_state = CURRENT_STATE_DISCHARGING;
        device->bms_system.current_mgr.data.total_current += fn_custom_data.data.zero_current;
        device->bms_system.current_mgr.data.total_current -= 300;
    }
    else
    {
        device->bms_system.current_mgr.data.current_state = CURRENT_STATE_CHARGING;
        device->bms_system.current_mgr.data.total_current += 300;
        device->bms_system.current_mgr.data.total_current -= fn_custom_data.data.zero_current;
    }

    /* 根据零点电流判断是否显示电流 */
    if (device->bms_system.current_mgr.data.total_current <= 0 ||
        device->bms_system.current_mgr.data.total_current <= fn_custom_data.data.zero_current)
    {
        printf("Current is less than zero, set to 0\n");
        device->bms_system.current_mgr.data.total_current = 0;
        device->bms_system.current_mgr.data.current_state = CURRENT_STATE_IDLE;
        device->bms_system.current_mgr.data.max_charge_current = 0;
        device->bms_system.current_mgr.data.max_discharge_current = 0;
    }
    printf("Total Current: %d mA %d \n", device->bms_system.current_mgr.data.total_current, device->ram[0].cur);
}

/**
 * @brief fn定制数据初始化函数
 * @note 用于初始化fn定制数据管理器的方法指针
 */
void fn_custom_data_init(void)
{
    fn_custom_data.method.fn_Rs2058_Init = fn_Rs2058_Init;
    fn_custom_data.method.fn_Rs2058_Gpio1_Low = fn_Rs2058_Gpio1_Low;
    fn_custom_data.method.fn_Rs2058_Gpio1_Hight = fn_Rs2058_Gpio1_Hight;
    fn_custom_data.method.fn_special_process = fn_special_process;
    fn_custom_data.method.fn_current_process = fn_current_process;
}

#endif