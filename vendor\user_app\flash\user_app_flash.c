#include "user_app_flash.h"
#include "flash.h"
#include "types.h"

#define FLASH_SECTOR_SIZE 4096
#define FLASH_START_ADDR  0x40000
#define FLASH_END_ADDR    0x50000
#define FLASH_PAGE_SIZE   256

/* Flash写入函数 - 优化版本 */
int My_Flash_Write(unsigned int addr, unsigned char *data, unsigned int len) 
{
    /* 参数检查 */
    if (addr < FLASH_START_ADDR || addr + len > FLASH_END_ADDR || data == NULL || len == 0)
    {
        return -1;
    }

    /* 计算涉及的扇区范围 */
    unsigned int start_sector = addr & (~(FLASH_SECTOR_SIZE - 1));
    unsigned int end_sector = ((addr + len - 1) & (~(FLASH_SECTOR_SIZE - 1))) + FLASH_SECTOR_SIZE;
    
    /* 扇区备份缓冲区 */
    static unsigned char sector_buf[FLASH_SECTOR_SIZE];
    
    /* 处理每个涉及的扇区 */
    for (unsigned int sector = start_sector; sector < end_sector; sector += FLASH_SECTOR_SIZE)
    {
        /* 计算当前扇区的写入范围 */
        unsigned int write_start = (addr > sector) ? addr : sector;
        unsigned int write_end = ((addr + len) < (sector + FLASH_SECTOR_SIZE)) ? 
                                (addr + len) : (sector + FLASH_SECTOR_SIZE);
        unsigned int write_len = write_end - write_start;
        
        if (write_len == 0) continue;
        
        /* 读取整个扇区到缓冲区 */
        flash_read_data(sector, FLASH_SECTOR_SIZE, sector_buf);
        
        /* 在缓冲区中更新需要写入的数据 */
        unsigned int buf_offset = write_start - sector;
        unsigned int data_offset = write_start - addr;
        for (unsigned int i = 0; i < write_len; i++)
        {
            sector_buf[buf_offset + i] = data[data_offset + i];
        }
        
        /* 擦除扇区并写回数据 */
        flash_erase_sector(sector);
        flash_write_page(sector, FLASH_SECTOR_SIZE, sector_buf);
    }

    return 0;
}

/* 高级Flash数据操作函数实现 */

/* 写入指定类型的数据 */
int Flash_Write_Data(flash_data_type_e type, unsigned char *data)
{
    const flash_data_config_t *config = Flash_Get_Config(type);
    if (!config || !data) return -1;
    
    /* 写入标志位 */
    unsigned char flag = USER_APP_FLASH_FLG;
    My_Flash_Write(config->flag_addr, &flag, 1);
    
    /* 写入数据 */
    return My_Flash_Write(config->data_addr, data, config->data_len);
}

/* 读取指定类型的数据 */
int Flash_Read_Data(flash_data_type_e type, unsigned char *data)
{
    const flash_data_config_t *config = Flash_Get_Config(type);
    if (!config || !data) return -1;
    
    /* 检查标志位验证数据是否有效 */
    unsigned char flag;
    flash_read_data(config->flag_addr, 1, &flag);
    
    /* 如果标志位不匹配，说明数据无效 */
    if (flag != USER_APP_FLASH_FLG)
    {
        return -1;  /* 数据无效 */
    }
    
    /* 标志位验证通过，读取数据 */
    flash_read_data(config->data_addr, config->data_len, data);
    return 0;
}

/* 检查数据是否有效 */
int Flash_Check_Data_Valid(flash_data_type_e type)
{
    const flash_data_config_t *config = Flash_Get_Config(type);
    if (!config) return 0;
    
    unsigned char flag;
    flash_read_data(config->flag_addr, 1, &flag);
    return (flag == USER_APP_FLASH_FLG) ? 1 : 0;
}

/* 清除指定类型的数据 */
void Flash_Clear_Data(flash_data_type_e type)
{
    const flash_data_config_t *config = Flash_Get_Config(type);
    if (!config) return;
    
    /* 清除标志位 */
    unsigned char clear_flag = 0xFF;
    My_Flash_Write(config->flag_addr, &clear_flag, 1);
}
