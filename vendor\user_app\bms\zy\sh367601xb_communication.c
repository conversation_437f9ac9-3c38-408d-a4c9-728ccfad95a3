#include "sh367601xb_communication.h"
#include "../../uart/app_usart.h"
/* ==========================================内部工具函数========================================== */
/**
 * @brief CRC8校验函数
 * @param data 数据指针
 * @param len 数据长度
 * @return CRC8校验值
 * @note 使用多项式0x07进行CRC8校验，用于通信数据完整性验证
 */
static unsigned char sh367601b_crc8(unsigned char *data, unsigned char len)
{
    unsigned char crc = 0x00;
    unsigned char i, j;

    for (i = 0; i < len; i++) {
        crc ^= data[i];
        for (j = 0; j < 8; j++) {
            if (crc & 0x80) {
                crc = (crc << 1) ^ 0x07;  /* 多项式：x^8 + x^2 + x^1 + 1 */
            } else {
                crc <<= 1;
            }
        }
    }
    return crc;
}
/* ==========================================通信驱动模块实现========================================== */
/**
 * @brief 复位*********芯片
 * @note 发送复位命令到芯片，使芯片重新启动
 */
void sh367601b_comm_reset(void)
{
    unsigned char cmd[5] = {0x1C, 0x0B, 0xBB, 0xCC, 0xFF};
    cmd[4] = sh367601b_crc8(cmd, 4);
    app_usart_fifo_push(cmd, 5);
}
/**
 * @brief 发送写命令到*********芯片
 * @note 将配置数据写入芯片ROM寄存器
 */
void sh367601b_comm_write_command(void)
{
    unsigned char cmd[5] = {0x1C, 0x0A, 0xAA, 0xBB, 0xFF};
    cmd[4] = sh367601b_crc8(cmd, 4);
    app_usart_fifo_push(cmd, 5);
}
/**
 * @brief 写入单个数据到指定地址
 * @param data 要写入的数据
 * @param addr 目标地址
 */
void sh367601b_comm_write_data(unsigned char data, unsigned char addr)
{
    unsigned char cmd[5] = {0x1C, 0x01, 0xFF, 0xFF, 0xFF};
    cmd[2] = addr;
    cmd[3] = data;
    cmd[4] = sh367601b_crc8(cmd, 4);
    app_usart_fifo_push(cmd, 5);
}
/**
 * @brief 读取ROM寄存器数据
 * @param addr 起始地址
 * @param recv_len 读取长度
 * @note 读取完成后需要手动调用解析函数：sh367601b_parser_parse_rom(device, received_data)
 */
void sh367601b_comm_read_rom(unsigned char addr, unsigned char recv_len)
{
    unsigned char cmd[5] = {0x1C, 0x02, 0xFF, 0xFF, 0xFF};
    cmd[2] = addr;
    cmd[3] = recv_len;
    cmd[4] = sh367601b_crc8(cmd, 4);
    app_usart_fifo_push(cmd, 5);
}
/**
 * @brief 读取RAM寄存器数据
 * @param addr 起始地址
 * @param recv_len 读取长度
 * @note 读取完成后需要手动调用解析函数：sh367601b_parser_parse_ram(device, received_data)
 */
void sh367601b_comm_read_ram(unsigned char addr, unsigned char recv_len)
{
    /* TODO: 调用底层通信模块读取RAM数据 */
    /* 示例：communication_read_ram(addr, recv_len, received_data); */
    /* 读取完成后需要手动调用解析函数：sh367601b_parser_parse_ram(device, received_data); */
    unsigned char cmd[5] = {0x1C, 0x03, 0xFF, 0xFF, 0xFF};
    cmd[2] = addr;
    cmd[3] = recv_len;
    cmd[4] = sh367601b_crc8(cmd, 4);
    app_usart_fifo_push(cmd, 5);
}