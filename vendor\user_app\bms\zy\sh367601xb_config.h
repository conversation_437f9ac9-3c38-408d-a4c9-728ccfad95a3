#ifndef SH367601XB_CONFIG_H
#define SH367601XB_CONFIG_H

#include "sh367601xb.h"

/* ==========================================配置管理模块声明========================================== */

/**
 * @brief 设置设备ID
 */
extern void sh367601b_config_set_id(SH367601B_Device* self, unsigned char id);

/**
 * @brief 设置放电MOSFET强制开启恢复延时控制位
 */
extern void sh367601b_config_set_enmosr(SH367601B_Device* self, unsigned char enmosr);

/**
 * @brief 设置退出过充电保护及充电高低温保护功能变化控制位
 */
extern void sh367601b_config_set_chys(SH367601B_Device* self, unsigned char chys);

/**
 * @brief 设置充电状态下充电温度保护延时
 */
extern void sh367601b_config_set_tc(SH367601B_Device* self, unsigned char tc);

/**
 * @brief 设置电池串数配置
 */
extern void sh367601b_config_set_cn(SH367601B_Device* self, unsigned char chip_index, unsigned char cn);

/**
 * @brief 设置均衡开启条件控制位
 */
extern void sh367601b_config_set_bals(SH367601B_Device* self, unsigned char bals);

/**
 * @brief 设置充电状态检测电压设置位
 */
extern void sh367601b_config_set_chs(SH367601B_Device* self, unsigned char chs);

/**
 * @brief 设置放电过流保护自恢复使能位
 */
extern void sh367601b_config_set_ocra(SH367601B_Device* self, unsigned char ocra);

/**
 * @brief 设置过充电保护恢复条件设置位
 */
extern void sh367601b_config_set_eovr(SH367601B_Device* self, unsigned char eovr);

/**
 * @brief 设置过放电保护恢复条件设置位
 */
extern void sh367601b_config_set_euvr(SH367601B_Device* self, unsigned char euvr);

/**
 * @brief 设置断线保护使能位
 */
extern void sh367601b_config_set_eow(SH367601B_Device* self, unsigned char eow);

/**
 * @brief 设置TS3温度检测点功能设置位
 */
extern void sh367601b_config_set_eot3(SH367601B_Device* self, unsigned char eot3);

/**
 * @brief 设置充放电MOSFET强制开启使能位
 */
extern void sh367601b_config_set_enmos(SH367601B_Device* self, unsigned char enmos);

/**
 * @brief 设置过充电保护延时
 */
extern void sh367601b_config_set_ovt(SH367601B_Device* self, unsigned short ovt);

/**
 * @brief 设置过充电保护电压
 */
extern void sh367601b_config_set_ov(SH367601B_Device* self, unsigned short ov);

/**
 * @brief 设置过充电恢复电压
 */
extern void sh367601b_config_set_ovr(SH367601B_Device* self, unsigned short ovr);

/**
 * @brief 设置过放电恢复电压
 */
extern void sh367601b_config_set_uvr(SH367601B_Device* self, unsigned short uvr);

/**
 * @brief 设置低电压禁止充电电压
 */
extern void sh367601b_config_set_lov(SH367601B_Device* self, short lov);

/**
 * @brief 设置均衡进入延时
 */
extern void sh367601b_config_set_balt(SH367601B_Device* self, unsigned char balt);

/**
 * @brief 设置过放电保护延时
 */
extern void sh367601b_config_set_uvt(SH367601B_Device* self, unsigned short uvt);

/**
 * @brief 设置过放电保护电压
 */
extern void sh367601b_config_set_uv(SH367601B_Device* self, unsigned short uv);

/**
 * @brief 设置均衡开启电压
 */
extern void sh367601b_config_set_balv(SH367601B_Device* self, unsigned short balv);

/**
 * @brief 设置均衡开启压差
 */
extern void sh367601b_config_set_bald(SH367601B_Device* self, unsigned char bald);

/**
 * @brief 设置放电过流1保护电压
 */
extern void sh367601b_config_set_ocd1v(SH367601B_Device* self, unsigned short ocd1v);

/**
 * @brief 设置放电过流1保护延时
 */
extern void sh367601b_config_set_ocd1t(SH367601B_Device* self, unsigned short ocd1t);

/**
 * @brief 设置短路保护延时
 */
extern void sh367601b_config_set_sct(SH367601B_Device* self, unsigned char sct);

/**
 * @brief 设置放电过流2保护电压
 */
extern void sh367601b_config_set_ocd2v(SH367601B_Device* self, unsigned char ocd2v);

/**
 * @brief 设置放电过流2保护延时
 */
extern void sh367601b_config_set_ocd2t(SH367601B_Device* self, short ocd2t);

/**
 * @brief 设置充电过流保护电压
 */
extern void sh367601b_config_set_occv(SH367601B_Device* self, short occv);

/**
 * @brief 设置充电过流保护延时
 */
extern void sh367601b_config_set_occt(SH367601B_Device* self, unsigned short occt);

/**
 * @brief 设置充电高温保护阈值
 */
extern void sh367601b_config_set_otc(SH367601B_Device* self, unsigned char otc);

/**
 * @brief 设置充电高温保护释放阈值
 */
extern void sh367601b_config_set_otcr(SH367601B_Device* self, unsigned char otcr);

/**
 * @brief 设置放电高温保护阈值
 */
extern void sh367601b_config_set_otd(SH367601B_Device* self, unsigned char otd);

/**
 * @brief 设置放电高温保护释放阈值
 */
extern void sh367601b_config_set_otdr(SH367601B_Device* self, unsigned char otdr);

/**
 * @brief 设置充电低温保护阈值
 */
extern void sh367601b_config_set_utc(SH367601B_Device* self, unsigned char utc);

/**
 * @brief 设置充电低温保护释放阈值
 */
extern void sh367601b_config_set_utcr(SH367601B_Device* self, unsigned char utcr);

/**
 * @brief 设置放电低温保护阈值
 */
extern void sh367601b_config_set_utd(SH367601B_Device* self, unsigned char utd);

/**
 * @brief 设置放电低温保护释放阈值
 */
extern void sh367601b_config_set_utdr(SH367601B_Device* self, unsigned char utdr);

/**
 * @brief 将ROM结构体数据打包成字节数组用于写入EEPROM
 */
extern void sh367601b_config_pack_rom_data(unsigned char *buffer, User_App_Sh3607601x_Rom_TypeDef *rom);

/**
 * @brief 创建ROM数据副本用于写入操作
 */
extern void sh367601b_config_create_rom_copy(SH367601B_Device* self);

#endif /* SH367601XB_CONFIG_H */
