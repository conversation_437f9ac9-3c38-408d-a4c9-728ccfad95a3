#include "bms_data_tool.h"
#include "bms_data.h"

/* ==========================================SOC========================================== */
/**
 * @brief  计算SOC
 * @param  soc         当前SOC（%）
 * @param  capacity_mAh 电池容量（mAh）
 * @param  current_mA  当前电流（mA）
 * @param  time_ms     时间间隔（ms）
 * @return 更新后的SOC（%）
 */
float soc_compute(float soc, float capacity_mAh, float current_mA, float time_ms, unsigned char direction)
{
    /* 1. 计算SOC变化量 */
    float delta_soc = (current_mA * time_ms) / (capacity_mAh * 3600.0f * 1000.0f) * 100.0f;
    
    /* 2. 更新SOC */
    if (CURRENT_STATE_DISCHARGING == direction) 
    {
        if (soc < 0.0f) soc = 0.0f;
        soc -= delta_soc;
    }
    else if (CURRENT_STATE_CHARGING == direction) 
    {
        soc += delta_soc;
        if (soc > 100.0f) soc = 100.0f;
    }  
    return soc;
}

/* ==========================================平滑滤波========================================== */
#define N 10
static float sma_buffer[N] = {0};
static int sma_index = 0;
static float ema = 0;
/**
 * @brief  平滑滤波
 * @param  new_sample 当前值
 * @return 滤波后的值
 */
float hybridFilter(float new_sample)
{
    /* EMA系数 */
    float alpha = 0.35;      
    /* SMA阶段 */
    float sum = 0;
    sma_buffer[sma_index] = new_sample;
    sma_index = (sma_index + 1) % N;
    for (int i = 0; i < N; i++) sum += sma_buffer[i];
    float sma = sum / N;
    
    /* EMA阶段 */
    ema = alpha * sma + (1 - alpha) * ema;
    return ema;
}
