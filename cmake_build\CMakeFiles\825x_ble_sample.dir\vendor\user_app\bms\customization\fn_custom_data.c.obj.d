CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/customization/fn_custom_data.c.obj: \
 D:\Telink_Project\BMS_Base\vendor\user_app\bms\customization\fn_custom_data.c \
 D:\Telink_Project\BMS_Base\vendor\user_app\bms\customization\/fn_custom_data.h \
 D:/Telink_Project/BMS_Base/./common/types.h \
 D:\Telink_Project\BMS_Base\vendor\user_app\bms\customization\/../../user_app_config.h \
 D:\Telink_Project\BMS_Base\vendor\user_app\bms\customization\/../zy/sh367601xb.h \
 D:\Telink_Project\BMS_Base\vendor\user_app\bms\customization\/../zy/../bms_data.h \
 D:\Telink_Project\BMS_Base\vendor\user_app\bms\customization\/../zy/../../user_app_config.h \
 D:\Telink_Project\BMS_Base\vendor\user_app\bms\customization\/../zy/../../list/list_type.h \
 D:\Telink_Project\BMS_Base\vendor\user_app\bms\customization\/../zy/../../list/queue/queue.h \
 c:\users\<USER>\.telink_tools\tc32_130_windows\tc32\bin\../lib/gcc/tc32-elf/4.5.1-tc32-1.3/include/stdint.h \
 c:\users\<USER>\.telink_tools\tc32_130_windows\tc32\bin\../lib/gcc/tc32-elf/4.5.1-tc32-1.3/../../../../tc32-elf/include/stdint.h \
 c:\users\<USER>\.telink_tools\tc32_130_windows\tc32\bin\../lib/gcc/tc32-elf/4.5.1-tc32-1.3/../../../../tc32-elf/include/_ansi.h \
 c:\users\<USER>\.telink_tools\tc32_130_windows\tc32\bin\../lib/gcc/tc32-elf/4.5.1-tc32-1.3/../../../../tc32-elf/include/newlib.h \
 c:\users\<USER>\.telink_tools\tc32_130_windows\tc32\bin\../lib/gcc/tc32-elf/4.5.1-tc32-1.3/../../../../tc32-elf/include/sys/config.h \
 c:\users\<USER>\.telink_tools\tc32_130_windows\tc32\bin\../lib/gcc/tc32-elf/4.5.1-tc32-1.3/../../../../tc32-elf/include/machine/ieeefp.h \
 c:\users\<USER>\.telink_tools\tc32_130_windows\tc32\bin\../lib/gcc/tc32-elf/4.5.1-tc32-1.3/../../../../tc32-elf/include/sys/features.h
