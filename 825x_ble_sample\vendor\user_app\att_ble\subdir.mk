################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../vendor/user_app/att_ble/app_ble.c 

OBJS += \
./vendor/user_app/att_ble/app_ble.o 


# Each subdirectory must supply rules for building sources it contributes
vendor/user_app/att_ble/%.o: ../vendor/user_app/att_ble/%.c vendor/user_app/att_ble/subdir.mk
	@echo 'Building file: $<'
	@echo 'Invoking: TC32 Compiler'
	tc32-elf-gcc -ffunction-sections -fdata-sections -I"D:\Telink_Project\FN_Project" -I"D:\Telink_Project\FN_Project\vendor\common" -I"D:\Telink_Project\FN_Project\common" -I"D:\Telink_Project\FN_Project\drivers\8258" -I"D:\Telink_Project\FN_Project\vendor\user_app\bms" -D__PROJECT_8258_BLE_SAMPLE__=1 -DCHIP_TYPE=CHIP_TYPE_825x -Wall -O2 -fpack-struct -fshort-enums -finline-small-functions -std=gnu99 -fshort-wchar -fms-extensions -c -o"$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


